import { CSSProperties } from "react";

import { ADDRESS_SEARCH_OPTIONS } from "../constants";

// 기본 타입 정의
export type AddressSearchTypes = keyof typeof ADDRESS_SEARCH_OPTIONS;
export type PagingType = "countable" | "unbounded";

// WKT 타입 정의 개선
type WKTPolygonType = `POLYGON(${string})` | `MULTIPOLYGON(${string})`;

// 검색 결과 타입 정의
export interface SearchResult<T = AddressBase> {
  resultList: T[];
  hasNextPage: boolean;
  totalCount?: number;
  currentPage?: number;
}

// 렌더링 클래스 이름 타입
export interface RenderAddressInfoLabelClassNames {
  roadAddress?: string;
  jibunAddress?: string;
  buildingName?: string;
  poiName?: string;
}

// 지리적 위치 정보
interface GeoLocation {
  srid: string;
  x?: number;
  y?: number;
  geometryWKT?: WKTPolygonType;
}

// 주소 정보
interface AddressInfo {
  roadAddress?: string;
  jibunAddress?: string;
  buildingName?: string;
  poiName?: string;
}

// 메인 주소 타입
export interface AddressBase extends GeoLocation, AddressInfo {}

// 옵션 타입들
export interface SearchTypesOptions {
  integrated?: boolean;
  jibun?: boolean;
  road?: boolean;
  roadApi?: boolean;
  building?: boolean;
  coordinates?: boolean;
  postalCode?: boolean;
  pnu?: boolean;
  poi?: boolean;
  [key: string]: boolean | undefined;
}

export interface VisibleFieldsOptions {
  roadAddress?: boolean;
  jibunAddress?: boolean;
  buildingName?: boolean;
  poiName?: boolean;
  [key: string]: boolean | undefined;
}

// 검색 필드 정의
export interface AddressSearchField {
  key: string;
  value: string;
  placeholder?: string;
  className?: string;
}

// 콜백 함수 타입 정의
export type SearchCallback = (
  fields: AddressSearchField[],
  type: AddressSearchTypes,
  page: number,
) => Promise<SearchResult | undefined> | SearchResult | undefined | void;

export type ItemClickCallback = (item: AddressBase) => void;

// 컴포넌트 Props 인터페이스들
export interface AddressSearchProps
  extends React.ComponentPropsWithoutRef<"div"> {
  pagingType?: PagingType;
  visibleFields?: VisibleFieldsOptions;
  useOneClickResultClose?: boolean;
  styleObject?: object;
  searchTypes?: SearchTypesOptions;
  onItemClick?: ItemClickCallback;
  onSearch?: SearchCallback;
  onLoadMore?: SearchCallback;
  haspage?: boolean;
  isLoading?: boolean;
  style?: CSSProperties;
}

export interface AddressSearchSelectProps {
  className?: string;
  style?: CSSProperties;
}

export interface AddressSearchInputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {}

export interface AddressSearchTriggerProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {}

export interface AddressSearchContentProps
  extends React.HTMLAttributes<HTMLDivElement> {}

export interface AddressSearchItemProps
  extends React.LiHTMLAttributes<HTMLLIElement> {
  item?: AddressBase;
  labelClassNames?: RenderAddressInfoLabelClassNames;
}

export interface AddressSearchRootProps
  extends React.HTMLAttributes<HTMLDivElement> {
  onSearch?: SearchCallback;
  onItemClick?: ItemClickCallback;
  searchTypes?: SearchTypesOptions;
  visibleFields?: VisibleFieldsOptions;
  customOptions?: Record<string, string>;
  defaultOpen?: boolean;
  pagingType?: PagingType;
  onLoadMore?: SearchCallback;
  haspage?: boolean;
  isLoading?: boolean;
}

export interface AddressSearchListProps
  extends React.HTMLAttributes<HTMLUListElement> {
  onLoadMore?: SearchCallback;
  haspage?: boolean;
  isLoading?: boolean;
}

export interface AddressSearchEmptyProps
  extends React.HTMLAttributes<HTMLDivElement> {
  message?: string;
  icon?: React.ReactNode;
}
