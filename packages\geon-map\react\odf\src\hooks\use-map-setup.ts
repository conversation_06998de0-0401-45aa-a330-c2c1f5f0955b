import type { MapInitializeOptions, ODF, ODF_MAP } from "@geon-map/core";
import { Map } from "@geon-map/core";
import { useCallback, useEffect, useMemo, useState } from "react";

import type { UseMapReturn } from "../types/map-types";

export interface UseMapSetupOptions extends MapInitializeOptions {
  containerRef: React.RefObject<HTMLDivElement>;
  autoInit?: boolean;
}

/**
 * 지도 초기화를 담당하는 Hook
 * Map 컴포넌트 내부에서 사용됩니다.
 */
export function useMapSetup(options: UseMapSetupOptions): UseMapReturn {
  const [map, setMap] = useState<ODF_MAP | null>(null);
  const [odf, setODF] = useState<ODF | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  const initialize = useCallback(async () => {
    console.log("지도 초기화 호출");
    console.log("컨테이너:", options.containerRef.current);
    console.log("초기화 여부:", isInitialized);
    if (!options.containerRef.current || isInitialized) return;

    try {
      console.log("지도 초기화 시작");
      //1.지도 시작
      const { mapInstance, odf: mapOdf } = await Map.createMap(
        options.containerRef.current,
        options,
      );
      window.odfMap = mapInstance;
      setMap(mapInstance);
      setODF(mapOdf);
      setIsInitialized(true);
    } catch (error) {
      console.error("Failed to initialize map:", error);
      throw error;
    }
  }, [
    isInitialized,
    options.containerRef,
    options.center,
    options.zoom,
    options.projection,
    options.basemap,
  ]);

  // 자동 초기화 - DOM이 준비된 후 실행 (한 번만)
  useEffect(() => {
    if (!options.autoInit || isInitialized) return;

    if (options.containerRef.current) {
      console.log("DOM 준비됨, 초기화 시작");
      initialize();
    } else {
      console.log("DOM 아직 준비 안됨, 대기 중...");
      // DOM이 준비될 때까지 짧은 간격으로 재시도
      const timer = setInterval(() => {
        if (options.containerRef.current) {
          console.log("DOM 준비됨 (재시도), 초기화 시작");
          clearInterval(timer);
          initialize();
        }
      }, 100);

      // 5초 후 타임아웃
      const timeout = setTimeout(() => {
        clearInterval(timer);
        console.error("DOM 준비 타임아웃");
      }, 5000);

      return () => {
        clearInterval(timer);
        clearTimeout(timeout);
      };
    }
  }, [options.autoInit, isInitialized]); // initialize 제거하여 무한 루프 방지

  // 옵션 변경 시 지도 업데이트
  useEffect(() => {
    if (map) {
      if (options.center) {
        Map.setCenter(map, options.center);
      }
      if (options.zoom !== undefined) {
        Map.setZoom(map, options.zoom);
      }
    }
  }, [map, options.center, options.zoom]);

  // 뷰 관련 메서드들
  const view = useMemo(
    () => ({
      setCenter: (center: [number, number], fromProjection?: string) => {
        if (!map) return;
        if (fromProjection) {
          const transformed = Map.transformCoordinate(
            map,
            center,
            fromProjection,
          );
          Map.setCenter(map, transformed);
        } else {
          Map.setCenter(map, center);
        }
      },
      setZoom: (zoom: number) => {
        if (!map) return;
        Map.setZoom(map, zoom);
      },
      setBasemap: (basemapId: string) => {
        if (!map?.basemapControl) return;
        Map.setBasemap(map, basemapId);
      },
      getCenter: (): [number, number] => {
        if (!map) return [0, 0];
        try {
          const viewState = Map.getViewState(map);
          return viewState.center;
        } catch {
          return [0, 0];
        }
      },
      getZoom: () => {
        if (!map) return 0;
        try {
          const viewState = Map.getViewState(map);
          return viewState.zoom;
        } catch {
          return 0;
        }
      },
      getProjection: () => {
        if (!map) return options.projection ?? "EPSG:5186";
        try {
          const viewState = Map.getViewState(map);
          return viewState.projection;
        } catch {
          return options.projection ?? "EPSG:5186";
        }
      },
      transformCoordinate: (
        coords: [number, number],
        sourceProjection: string,
      ) => {
        if (!map) return coords;
        return Map.transformCoordinate(map, coords, sourceProjection);
      },
      moveToCurrentLocation: (zoom?: number) => {
        if (!map || !navigator?.geolocation) return;

        navigator.geolocation.getCurrentPosition(
          (position) => {
            const { latitude, longitude } = position.coords;
            const coords: [number, number] = [longitude, latitude];
            const targetCoords = Map.transformCoordinate(
              map,
              coords,
              "EPSG:4326",
            );
            Map.setCenter(map, targetCoords);
            if (zoom !== undefined) {
              Map.setZoom(map, zoom);
            }
          },
          (error) => {
            console.error("Geolocation error:", error);
          },
        );
      },
    }),
    [map, options.projection],
  );

  return {
    odf,
    map,
    view,
    isLoading: !isInitialized && options.autoInit === true,
    error: null,
    layer: {
      add: async () => null,
      remove: () => {},
      toggle: () => {},
      getAll: () => [],
      updateStyle: async () => {},
      updateFilter: async () => {},
    },
    control: {
      draw: () => {},
    },
  };
}
