"use client";
import { useLayer } from "@geon-map/react-odf";
import { useCallback, useEffect, useState } from "react";

type HighlightOptions = {
  isFitToLayer?: boolean;
  srid?: string;
};

export function useHighlightLayer() {
  const { addLayer, addFeature, clearFeatures, fitToLayer, setMaxZIndex } =
    useLayer();
  const [layerId, setLayerId] = useState<string | null>(null);

  useEffect(() => {
    addLayer({ type: "empty" }).then((id) => {
      setLayerId(id);
    });
  }, [addLayer]);

  const highlight = useCallback(
    (feature: any, options: HighlightOptions = {}) => {
      console.log("feature 및 options", feature, options);
      if (!feature || !layerId) return;

      const { isFitToLayer = true, srid } = options;

      clearFeatures(layerId);
      addFeature(layerId, feature, { srid: srid });
      if (isFitToLayer) fitToLayer(layerId);
      setMaxZIndex(layerId);
    },
    [layerId, clearFeatures, addFeature],
  );

  const clearHighlight = useCallback(() => {
    if (!layerId) return;
    clearFeatures(layerId);
  }, [layerId, clearFeatures]);

  return {
    highlight,
    clearHighlight,
    layerId,
  };
}
