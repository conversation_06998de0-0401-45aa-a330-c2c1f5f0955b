import { fetcher } from "../utils/fetcher";
import { API_TYPE, apiUrl } from "../utils/geonAPI";
import {
  AddressBldRequest,
  AddressCoordRequest,
  AddressIntRequest,
  AddressJibunRequest,
  AddressPnuRequest,
  AddressPoiRequest,
  AddressRoadLinkRequest,
  AddressRoadRequest,
  AddressSearchResponse,
} from "./type/addrgeo-type";

const _API_TYPE: API_TYPE = "addrgeo";

// 설정 타입
export interface AddrgeoAPIConfig {
  baseUrl?: string;
  apiKey?: string;
  timeout?: number;
}

// 동적 API 클라이언트 생성 함수
export function createGeonAddrgeoClient(config: AddrgeoAPIConfig = {}) {
  const { baseUrl, apiKey } = config;

  return {
    address: {
      /** 건물 주소 검색 */
      bld: (params: AddressBldRequest) =>
        fetcher.get<AddressSearchResponse>(
          apiUrl({
            endpoint: "/address/bld",
            type: _API_TYPE,
            baseUrl,
            apiKey,
          }),
          params,
        ),

      /** 통합 주소 검색 */
      int: (params: AddressIntRequest) =>
        fetcher.get<AddressSearchResponse>(
          apiUrl({
            endpoint: "/address/int",
            type: _API_TYPE,
            baseUrl,
            apiKey,
          }),
          params,
        ),

      /** 지번 주소 검색 */
      jibun: (params: AddressJibunRequest) =>
        fetcher.get<AddressSearchResponse>(
          apiUrl({
            endpoint: "/address/jibun",
            type: _API_TYPE,
            baseUrl,
            apiKey,
          }),
          params,
        ),
      /** 도로명 주소 검색 */
      road: (params: AddressRoadRequest) =>
        fetcher.get<AddressSearchResponse>(
          apiUrl({
            endpoint: "/address/road",
            type: _API_TYPE,
            baseUrl,
            apiKey,
          }),
          params,
        ),

      /** 도로 링크 기반 주소 검색 */
      roadLink: (params: AddressRoadLinkRequest) =>
        fetcher.get<AddressSearchResponse>(
          apiUrl({
            endpoint: "/address/road/link",
            type: _API_TYPE,
            baseUrl,
            apiKey,
          }),
          params,
        ),

      /** 좌표 → 주소 변환 */
      coord: (params: AddressCoordRequest) =>
        fetcher.get<AddressSearchResponse>(
          apiUrl({
            endpoint: "/address/coord",
            type: _API_TYPE,
            baseUrl,
            apiKey,
          }),
          params,
        ),

      /** PNU 기반 주소 조회 */
      pnu: (params: AddressPnuRequest) =>
        fetcher.get<AddressSearchResponse>(
          apiUrl({
            endpoint: "/address/pnu",
            type: _API_TYPE,
            baseUrl,
            apiKey,
          }),
          params,
        ),

      /** POI 주소 검색 */
      poi: (params: AddressPoiRequest) =>
        fetcher.get<AddressSearchResponse>(
          apiUrl({
            endpoint: "/address/poi",
            type: _API_TYPE,
            baseUrl,
            apiKey,
          }),
          params,
        ),

      /** 기초구역번호 검색 */
      basic: (params: AddressBldRequest) =>
        fetcher.get<AddressSearchResponse>(
          apiUrl({
            endpoint: "/address/basic",
            type: _API_TYPE,
            baseUrl,
            apiKey,
          }),
          params,
        ),
    },
  };
}

export const defaultGeonAddrgeoClient = createGeonAddrgeoClient();
