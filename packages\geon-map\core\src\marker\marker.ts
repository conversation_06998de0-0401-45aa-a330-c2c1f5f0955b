import { SingletonBase } from "@/SingletonBase";

import type { Coordinate, MarkerEventHandlers, MarkerOptions } from "../types";
import { generateId } from "../utils/coordinate";
import { MarkerFactory } from "./marker-factory";

/**
 * ODF Marker 핵심 클래스
 * 마커 생성, 관리, 이벤트 처리를 담당
 */
export class Marker extends SingletonBase {
  constructor() {
    super("Marker");
  }
  static getInstance(): Marker {
    const instance = super._getInstance.call(this, "Marker 싱글톤") as Marker;
    return instance;
  }
  /**
   * 마커 생성 및 지도에 추가
   */
  static createAndAddMarker(
    map: any,
    options: MarkerOptions,
    eventHandlers?: MarkerEventHandlers,
  ): { markerId: string; odfMarker: any; listeners?: Map<string, any> } {
    if (!map) {
      throw new Error("Map instance is required");
    }

    try {
      // ODF 마커 생성
      const odfMarker = MarkerFactory.createODFMarker(options);

      // 지도에 추가
      odfMarker.setMap(map);

      // 마커 ID 생성
      const markerId = options.id || generateId("marker");

      // 이벤트 리스너 등록
      let listeners: Map<string, any> | undefined;
      if (eventHandlers) {
        listeners = MarkerFactory.registerEventListeners(
          odfMarker,
          eventHandlers,
        );
      }

      return { markerId, odfMarker, listeners };
    } catch (error) {
      console.error("Failed to create marker:", error);
      throw error;
    }
  }

  /**
   * 마커 제거
   */
  static removeMarker(odfMarker: any, listeners?: Map<string, any>): void {
    if (!odfMarker) {
      throw new Error("Marker instance is required");
    }

    try {
      // 이벤트 리스너 제거
      if (listeners) {
        MarkerFactory.removeEventListeners(listeners);
      }

      // 지도에서 제거
      // odfMarker.setMap(map);
      odfMarker.removeMap();
    } catch (error) {
      console.error("Failed to remove marker:", error);
      throw error;
    }
  }

  /**
   * 마커 위치 설정
   */
  static setMarkerPosition(odfMarker: any, position: Coordinate): void {
    if (!odfMarker) {
      throw new Error("Marker instance is required");
    }

    try {
      odfMarker.setPosition(position);
    } catch (error) {
      console.error("Failed to set marker position:", error);
      throw error;
    }
  }

  /**
   * 마커 가시성 설정
   */
  static setMarkerVisible(map: any, odfMarker: any, visible: boolean): void {
    if (!odfMarker) {
      throw new Error("Marker instance is required");
    }

    try {
      if (visible) {
        odfMarker.setMap(map);
      } else {
        odfMarker.setMap(null);
      }
    } catch (error) {
      console.error("Failed to set marker visibility:", error);
      throw error;
    }
  }

  /**
   * 마커 드래그 가능 여부 설정
   */
  static setMarkerDraggable(odfMarker: any, draggable: boolean): void {
    if (!odfMarker) {
      throw new Error("Marker instance is required");
    }

    try {
      odfMarker.setDraggable(draggable);
    } catch (error) {
      console.error("Failed to set marker draggable:", error);
      throw error;
    }
  }

  /**
   * 마커 스타일 설정
   */
  static setMarkerStyle(odfMarker: any, style: any): void {
    if (!odfMarker) {
      throw new Error("Marker instance is required");
    }

    try {
      odfMarker.setStyle(style);
    } catch (error) {
      console.error("Failed to set marker style:", error);
      throw error;
    }
  }
}
