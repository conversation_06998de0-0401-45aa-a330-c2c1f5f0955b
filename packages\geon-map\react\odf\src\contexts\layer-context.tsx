"use client";

import { createContext, ReactNode, use, useReducer } from "react";

import type { Layer } from "../types/layer-types";

export interface LayerContextState {
  layers: Layer[];
  selectedLayerId?: string;
  expandedGroups: Set<string>;
}

export type LayerAction =
  | { type: "ADD_LAYER"; payload: Layer }
  | { type: "REMOVE_LAYER"; payload: string }
  | { type: "UPDATE_LAYER"; payload: { id: string; updates: Partial<Layer> } }
  | { type: "SET_LAYERS"; payload: Layer[] }
  | { type: "TOGGLE_LAYER_VISIBILITY"; payload: string }
  | { type: "SET_LAYER_FILTER"; payload: { id: string; filter: string } }
  | { type: "SET_SELECTED_LAYER"; payload: string }
  | { type: "TOGGLE_GROUP"; payload: string };

const initialState: LayerContextState = {
  layers: [],
  selectedLayerId: undefined,
  expandedGroups: new Set(),
};

function layerReducer(
  state: LayerContextState,
  action: LayerAction,
): LayerContextState {
  switch (action.type) {
    case "ADD_LAYER":
      return {
        ...state,
        layers: [...state.layers, action.payload],
      };

    case "REMOVE_LAYER":
      return {
        ...state,
        layers: state.layers.filter((layer) => layer.id !== action.payload),
      };

    case "UPDATE_LAYER":
      return {
        ...state,
        layers: state.layers.map((layer) =>
          layer.id === action.payload.id
            ? { ...layer, ...action.payload.updates }
            : layer,
        ),
      };

    case "SET_LAYERS":
      return {
        ...state,
        layers: action.payload,
      };

    case "TOGGLE_LAYER_VISIBILITY":
      return {
        ...state,
        layers: state.layers.map((layer) =>
          layer.id === action.payload
            ? { ...layer, visible: !layer.visible }
            : layer,
        ),
      };

    case "SET_LAYER_FILTER":
      return {
        ...state,
        layers: state.layers.map((layer) =>
          layer.id === action.payload.id
            ? { ...layer, filter: action.payload.filter }
            : layer,
        ),
      };

    case "SET_SELECTED_LAYER":
      return {
        ...state,
        selectedLayerId: action.payload,
      };

    case "TOGGLE_GROUP": {
      const newExpandedGroups = new Set(state.expandedGroups);
      if (newExpandedGroups.has(action.payload)) {
        newExpandedGroups.delete(action.payload);
      } else {
        newExpandedGroups.add(action.payload);
      }
      return {
        ...state,
        expandedGroups: newExpandedGroups,
      };
    }
    default:
      return state;
  }
}

export const LayerContext = createContext<{
  state: LayerContextState;
  dispatch: React.Dispatch<LayerAction>;
} | null>(null);

export function LayerProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(layerReducer, initialState);

  return (
    <LayerContext.Provider value={{ state, dispatch }}>
      {children}
    </LayerContext.Provider>
  );
}

export function useLayerContext() {
  const context = use(LayerContext);
  if (!context) {
    throw new Error("useLayerContext must be used within a LayerProvider");
  }
  return context;
}
