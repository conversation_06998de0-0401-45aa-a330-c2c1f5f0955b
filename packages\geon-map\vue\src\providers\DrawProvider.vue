<template>
  <slot />
</template>

<script setup lang="ts">
import { watch, onMounted } from 'vue'
import { useMapStore } from '../stores/mapStore'

// DrawProvider Props - React 버전과 동일한 속성들
interface DrawProviderProps {
  /** 연속 측정 여부 */
  continuity?: boolean
  /** 측정 옵션 활성화 여부 (선 그리기/원그리기 툴에서 활성화) */
  measure?: boolean
  /** drawControl 생성 시 새 레이어 생성 여부 */
  createNewLayer?: boolean
  /** 우클릭 편집 기능 */
  editFeatureMenu?: string[]
  /** 생성할 툴 배열 */
  tools?: any[]
  /** 툴팁 메세지 변경 */
  message?: any
  /** 그리기 도형 스타일 */
  style?: any
  /** 버퍼 도형 스타일 */
  bufferStyle?: any
}

const props = withDefaults(defineProps<DrawProviderProps>(), {
  continuity: false,
  measure: false,
  createNewLayer: true,
  editFeatureMenu: () => [],
  tools: () => [],
  message: () => ({}),
  style: () => ({
    fill: {
      color: [254, 243, 255, 0.6],
    },
    stroke: {
      color: [103, 87, 197, 0.7],
      width: 2,
    },
    image: {
      circle: {
        fill: {
          color: [254, 243, 255, 0.6],
        },
        stroke: {
          color: [103, 87, 197, 0.7],
          width: 2,
        },
        radius: 5,
      },
    },
  }),
  bufferStyle: () => ({})
})

// Nuxt.js 환경에서 안전하게 store 사용
let mapStore: ReturnType<typeof useMapStore>

try {
  mapStore = useMapStore()
} catch (error) {
  console.warn('MapStore not available in DrawProvider:', error)
  // 빈 객체로 fallback (실제 기능은 작동하지 않지만 오류 방지)
  mapStore = {
    map: null,
    drawManager: null,
    initializeDrawManager: () => {}
  } as any
}

// DrawControlOptions 생성
const getDrawControlOptions = () => {
  const { continuity, measure, createNewLayer, editFeatureMenu, tools, message, style, bufferStyle } = props
  
  return {
    continuity,
    measure,
    createNewLayer,
    editFeatureMenu,
    tools,
    message,
    style,
    bufferStyle,
  }
}

// 지도가 초기화된 후 DrawManager 초기화
const initializeDrawManagerIfReady = () => {
  if (mapStore.map && !mapStore.drawManager) {
    const drawControlOptions = getDrawControlOptions()
    mapStore.initializeDrawManager(drawControlOptions)
  }
}

onMounted(() => {
  // 지도가 이미 초기화되어 있다면 즉시 DrawManager 초기화
  initializeDrawManagerIfReady()
})

// 지도 상태 변화 감지
watch(() => mapStore.map, (newMap) => {
  if (newMap && !mapStore.drawManager) {
    const drawControlOptions = getDrawControlOptions()
    mapStore.initializeDrawManager(drawControlOptions)
  }
})

// Props 변화 감지하여 DrawManager 재초기화
watch(() => props, (newProps) => {
  if (mapStore.map && mapStore.drawManager) {
    // DrawManager 재초기화
    mapStore.drawManager.destroy()
    const drawControlOptions = getDrawControlOptions()
    mapStore.initializeDrawManager(drawControlOptions)
  }
}, { deep: true })
</script>
