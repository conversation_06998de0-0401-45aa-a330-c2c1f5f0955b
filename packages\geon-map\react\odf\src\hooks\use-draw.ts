import type {
  ContextMenuInfo,
  DrawingFeature,
  DrawingMode,
  MeasureFeature,
  MeasureMode,
  SelectedFeature,
} from "@geon-map/core";
import { useCallback } from "react";

import { useMapStore } from "../stores/map-store";

// 레거시 호환성을 위한 타입 재export
export type {
  ContextMenuInfo,
  DrawingFeature,
  DrawingMode,
  MeasureFeature,
  MeasureMode,
  SelectedFeature,
};

// 레거시 호환성을 위한 Drawing 타입 (DrawingFeature와 동일)
export type Drawing = DrawingFeature;

/**
 * 지도 그리기 및 측정 기능을 제공하는 Hook
 *
 * 실제 ODF DrawControl과 MeasureControl을 사용하여 그리기 및 측정 기능을 제공합니다.
 * MapProvider에서 DrawManager가 초기화되어야 정상적으로 동작합니다.
 * 측정 기능을 사용하려면 DrawProvider에서 measureOptions를 제공해야 합니다.
 *
 * @example
 * ```tsx
 * function DrawingTools() {
 *   const {
 *     startDrawing,
 *     stopDrawing,
 *     startMeasuring,
 *     stopMeasuring,
 *     features,
 *     drawingMode,
 *     isDrawing
 *   } = useDraw();
 *
 *   return (
 *     <div>
 *       <button onClick={() => startDrawing('lineString')}>선 그리기</button>
 *       <button onClick={() => startDrawing('polygon')}>다각형 그리기</button>
 *       <button onClick={() => startDrawing('point')}>점 그리기</button>
 *       <button onClick={() => startDrawing('circle')}>원 그리기</button>
 *
 *       <button onClick={() => startMeasuring('measure-distance')}>거리 측정</button>
 *       <button onClick={() => startMeasuring('measure-area')}>면적 측정</button>
 *       <button onClick={() => startMeasuring('measure-round')}>반경 측정</button>
 *       <button onClick={() => startMeasuring('measure-spot')}>좌표 측정</button>
 *
 *       <button onClick={stopDrawing}>중지</button>
 *       <button onClick={clearAllFeatures}>모두 삭제</button>
 *       <p>현재 모드: {drawingMode}</p>
 *       <p>활성 상태: {isDrawing ? '예' : '아니오'}</p>
 *       <p>도형/측정 수: {features.length}</p>
 *     </div>
 *   );
 * }
 * ```
 */
export function useDraw() {
  // Zustand store에서 그리기 관련 상태 구독
  const drawManager = useMapStore((state) => state.drawManager);
  const features = useMapStore((state) => state.features);
  const drawingMode = useMapStore((state) => state.drawingMode);
  const selectedFeature = useMapStore((state) => state.selectedFeature);
  const contextMenuInfo = useMapStore((state) => state.contextMenuInfo);

  // Store 액션들
  const {
    startDrawing: storeStartDrawing,
    stopDrawing: storeStopDrawing,
    startMeasuring: storeStartMeasuring,
    stopMeasuring: storeStopMeasuring,
    clearFeatures,
    removeFeature,
    setSelectedFeature,
    setContextMenuInfo,
    deleteSelectedFeature,
    changeSelectedFeatureStyle,
    undo,
    redo,
    canUndo,
    canRedo,
  } = useMapStore.getState();

  // 그리기 시작 (그리기 모드와 측정 모드 모두 지원)
  const startDrawing = useCallback(
    (mode: DrawingMode) => {
      if (!drawManager || !mode) {
        console.warn("DrawManager is not initialized or invalid mode");
        return;
      }

      // 측정 모드인지 확인
      if (mode.startsWith("measure-")) {
        storeStartMeasuring(mode as MeasureMode);
      } else {
        storeStartDrawing(mode);
      }
    },
    [drawManager, storeStartDrawing, storeStartMeasuring],
  );

  // 그리기 중지 (그리기 모드와 측정 모드 모두 지원)
  const stopDrawing = useCallback(() => {
    if (!drawManager) {
      console.warn("DrawManager is not initialized");
      return;
    }

    // 현재 모드에 따라 적절한 중지 함수 호출
    if (drawingMode && drawingMode.startsWith("measure-")) {
      storeStopMeasuring();
    } else {
      storeStopDrawing();
    }
  }, [drawManager, drawingMode, storeStopDrawing, storeStopMeasuring]);

  // 현재 그리기/측정 취소 (진행 중인 작업만 취소)
  const cancelCurrentDrawing = useCallback(() => {
    if (!drawManager) {
      console.warn("DrawManager is not initialized");
      return;
    }

    drawManager.cancelCurrentDrawing();

    // 상태 초기화
    if (drawingMode && drawingMode.startsWith("measure-")) {
      storeStopMeasuring();
    } else {
      storeStopDrawing();
    }
  }, [drawManager, drawingMode, storeStopDrawing, storeStopMeasuring]);

  // 그리기 삭제
  const deleteFeature = useCallback(
    (featureId: string) => {
      removeFeature(featureId);
      console.log("그리기 삭제:", featureId);
    },
    [removeFeature],
  );

  // 모든 그리기 삭제
  const clearAllFeatures = useCallback(() => {
    clearFeatures();
    console.log("모든 그리기 삭제");
  }, [clearFeatures]);

  // DrawManager 상태 조회 (React store에서 관리)
  const getDrawState = useCallback(() => {
    const isDrawingMode =
      drawingMode !== null && !drawingMode.startsWith("measure-");
    const isMeasuringMode =
      drawingMode !== null && drawingMode.startsWith("measure-");

    return {
      activeMode: drawingMode,
      features: features,
      isDrawing: drawingMode !== null,
      isDrawingMode,
      isMeasuringMode,
      isContinuous: false, // 필요시 store에 추가
    };
  }, [drawingMode, features]);

  // 그리기 레이어 조회
  const getDrawLayer = useCallback(() => {
    if (!drawManager) return null;
    return drawManager.getDrawLayer();
  }, [drawManager]);

  // DrawControl 인스턴스 조회 (고급 사용자용)
  const getDrawControl = useCallback(() => {
    if (!drawManager) return null;
    return drawManager.getDrawControl();
  }, [drawManager]);

  // GeoJSON 내보내기
  const exportToGeoJSON = useCallback(() => {
    if (!drawManager) {
      console.warn("DrawManager is not initialized");
      return null;
    }
    return drawManager.exportToGeoJSON();
  }, [drawManager]);

  // GeoJSON 불러오기 (Stateless)
  const importFromGeoJSON = useCallback(
    (geoJsonData: any, styleOptions?: any) => {
      if (!drawManager) {
        console.warn("DrawManager is not initialized");
        return [];
      }

      // DrawManager에서 처리된 features 반환받기
      const importedFeatures = drawManager.importFromGeoJSON(
        geoJsonData,
        styleOptions,
      );

      if (importedFeatures.length > 0) {
        // React store에 추가
        const { setFeatures } = useMapStore.getState();
        const currentFeatures = useMapStore.getState().features;
        setFeatures([...currentFeatures, ...importedFeatures]);
      }

      return importedFeatures;
    },
    [drawManager],
  );

  // 선택된 feature 삭제
  const handleDeleteSelectedFeature = useCallback(() => {
    deleteSelectedFeature();
  }, [deleteSelectedFeature]);

  // 선택된 feature 스타일 변경
  const handleStyleChange = useCallback(
    (styleOptions: any) => {
      changeSelectedFeatureStyle(styleOptions);
    },
    [changeSelectedFeatureStyle],
  );

  // 컨텍스트 메뉴 닫기
  const handleCloseContextMenu = useCallback(() => {
    // 상태 초기화 순서 개선: contextMenuInfo를 먼저 null로 설정하여
    // Popup 컴포넌트가 안전하게 언마운트되도록 함
    setContextMenuInfo(null);

    // 약간의 지연을 두고 selectedFeature 초기화
    setTimeout(() => {
      setSelectedFeature(null);
    }, 0);
  }, [setContextMenuInfo, setSelectedFeature]);

  // Undo/Redo 핸들러
  const handleUndo = useCallback(() => {
    undo();
  }, [undo]);

  const handleRedo = useCallback(() => {
    redo();
  }, [redo]);

  // 측정 전용 함수들
  const startMeasuring = useCallback(
    (mode: MeasureMode) => {
      if (!drawManager) {
        console.warn("DrawManager is not initialized");
        return;
      }

      storeStartMeasuring(mode);
    },
    [drawManager, storeStartMeasuring],
  );

  const stopMeasuring = useCallback(() => {
    if (!drawManager) {
      console.warn("DrawManager is not initialized");
      return;
    }

    storeStopMeasuring();
  }, [drawManager, storeStopMeasuring]);

  // 측정 관련 유틸리티
  const getMeasureManager = useCallback(() => {
    if (!drawManager) return null;
    return drawManager.getMeasureManager();
  }, [drawManager]);

  const getMeasureControl = useCallback(() => {
    if (!drawManager) return null;
    return drawManager.getMeasureControl();
  }, [drawManager]);

  const getMeasureLayer = useCallback(() => {
    if (!drawManager) return null;
    return drawManager.getMeasureLayer();
  }, [drawManager]);

  return {
    // 상태
    features,
    drawingMode,
    isDrawing: drawingMode !== null,
    drawManager,
    selectedFeature,
    contextMenuInfo,

    // 기본 액션
    startDrawing,
    stopDrawing,
    cancelCurrentDrawing,

    // 그리기 관리
    deleteFeature,
    clearAllFeatures,

    // 선택된 feature 관련
    handleDeleteSelectedFeature,
    handleStyleChange,
    handleCloseContextMenu,

    // Undo/Redo 기능
    handleUndo,
    handleRedo,
    canUndo: canUndo(),
    canRedo: canRedo(),

    // GeoJSON 저장/불러오기
    exportToGeoJSON,
    importFromGeoJSON,

    // 고급 기능
    getDrawState,
    getDrawLayer,
    getDrawControl,

    // 측정 기능
    startMeasuring,
    stopMeasuring,
    getMeasureManager,
    getMeasureControl,
    getMeasureLayer,
  };
}
