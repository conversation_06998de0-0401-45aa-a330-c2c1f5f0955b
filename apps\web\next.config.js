import NextBundleAnalyzer from "@next/bundle-analyzer";

/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  output: "standalone",
  experimental: {
    optimizePackageImports: ["lucide-react"],
  },
  transpilePackages: [
    "web",
    "@config/*",
    "@geon-ui/react",
    "@geon-query/*",
    "@geon-map/*",
  ],
};

const analyze = process.env.ANALYZE === "true";
const withBundleAnalyze = NextBundleAnalyzer({
  enabled: analyze,
});

export default withBundleAnalyze(nextConfig);
