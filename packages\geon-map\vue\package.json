{"name": "@geon-map/vue", "version": "0.0.1", "description": "Vue.js components and composables for geon-map", "type": "module", "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"build": "vite build", "type-check": "vue-tsc --noEmit"}, "peerDependencies": {"pinia": "^2.0.0", "vue": "^3.3.0"}, "dependencies": {"@geon-map/core": "workspace:*"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.4", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-plugin-dts": "^4.5.4", "vue-tsc": "^2.2.12"}}