import type {
  DrawControlOptions,
  DrawingFeature,
  DrawingMode,
  MapInitializeOptions,
  MeasureControlOptions,
  MeasureFeature,
  MeasureMode,
  ODF,
  ODF_MAP,
} from "@geon-map/core";
import type { ContextMenuInfo, SelectedFeature } from "@geon-map/core";
import { Draw, Feature } from "@geon-map/core";
import { create } from "zustand";
import { devtools } from "zustand/middleware";

// 지도 상태 인터페이스
export interface MapState {
  odf: ODF | null;
  // 지도 인스턴스
  map: ODF_MAP | null;
  isLoading: boolean;
  error: string | null;

  // 지도 설정
  mapOptions: MapInitializeOptions;

  // 레이어 상태
  layers: any[];
  selectedLayerId: string | null;

  // 그리기 상태
  drawManager: Draw | null;
  isInitializingDrawManager: boolean;
  isDrawManagerEventsSetup: boolean;
  features: DrawingFeature[];
  drawingMode: DrawingMode;
  selectedFeature: SelectedFeature | null;
  contextMenuInfo: ContextMenuInfo | null;

  // Undo/Redo 상태
  undoStack: DrawingFeature[][];
  redoStack: DrawingFeature[][];

  // 마커 상태
  markers: any[];

  // 측정 상태
  measurements: any[];
}

// 지도 액션 인터페이스
export interface MapActions {
  // 지도 관련 액션
  setMap: (map: ODF_MAP) => void;
  setOdf: (odf: ODF) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  updateMapOptions: (options: Partial<MapInitializeOptions>) => void;

  // 레이어 관련 액션
  addLayer: (layer: any) => void;
  removeLayer: (layerId: string) => void;
  updateLayer: (layerId: string, updates: any) => void;
  setSelectedLayer: (layerId: string | null) => void;

  // 그리기 관련 액션
  setDrawManager: (drawManager: Draw | null) => void;
  initializeDrawManager: (
    options?: DrawControlOptions,
    measureOptions?: MeasureControlOptions,
  ) => void;
  setupDrawManagerEvents: (drawManager: Draw) => void;
  addFeature: (feature: DrawingFeature) => void;
  removeFeature: (featureId: string) => void;
  setDrawingMode: (mode: DrawingMode) => void;
  setFeatures: (features: DrawingFeature[]) => void;
  clearFeatures: () => void;
  startDrawing: (mode: DrawingMode) => void;
  stopDrawing: () => void;

  // 측정 관련 액션
  startMeasuring: (mode: MeasureMode) => void;
  stopMeasuring: () => void;
  addMeasureFeature: (feature: MeasureFeature) => void;

  // 선택된 feature 관련 액션
  setSelectedFeature: (feature: SelectedFeature | null) => void;
  setContextMenuInfo: (info: ContextMenuInfo | null) => void;
  deleteSelectedFeature: () => void;
  changeSelectedFeatureStyle: (styleOptions: any) => void;

  // Undo/Redo 관련 액션
  undo: () => void;
  redo: () => void;
  canUndo: () => boolean;
  canRedo: () => boolean;
  saveStateToHistory: () => void;

  // 마커 관련 액션
  addMarker: (marker: any) => void;
  removeMarker: (markerId: string) => void;
  updateMarker: (markerId: string, updates: any) => void;
  clearMarkers: () => void;

  // 측정 관련 액션
  addMeasurement: (measurement: any) => void;
  removeMeasurement: (measurementId: string) => void;
  clearMeasurements: () => void;

  // 전체 초기화
  reset: () => void;
}

// 초기 상태
const initialState: MapState = {
  odf: null,
  map: null,
  isLoading: false,
  error: null,
  mapOptions: {},
  layers: [],
  selectedLayerId: null,
  drawManager: null,
  isInitializingDrawManager: false,
  isDrawManagerEventsSetup: false,
  features: [],
  drawingMode: null,
  selectedFeature: null,
  contextMenuInfo: null,
  undoStack: [],
  redoStack: [],
  markers: [],
  measurements: [],
};

// Zustand Store 생성
export const useMapStore = create<MapState & MapActions>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // 지도 관련 액션
      setOdf: (odf) => set({ odf }, false, "setOdf"),
      setMap: (map) => set({ map }, false, "setMap"),
      setLoading: (isLoading) => set({ isLoading }, false, "setLoading"),
      setError: (error) => set({ error }, false, "setError"),
      updateMapOptions: (options) =>
        set(
          (state) => ({ mapOptions: { ...state.mapOptions, ...options } }),
          false,
          "updateMapOptions",
        ),

      // 레이어 관련 액션
      addLayer: (layer) =>
        set(
          (state) => ({ layers: [...state.layers, layer] }),
          false,
          "addLayer",
        ),
      removeLayer: (layerId) =>
        set(
          (state) => ({
            layers: state.layers.filter((layer) => layer.id !== layerId),
            selectedLayerId:
              state.selectedLayerId === layerId ? null : state.selectedLayerId,
          }),
          false,
          "removeLayer",
        ),
      updateLayer: (layerId, updates) =>
        set(
          (state) => ({
            layers: state.layers.map((layer) =>
              layer.id === layerId ? { ...layer, ...updates } : layer,
            ),
          }),
          false,
          "updateLayer",
        ),
      setSelectedLayer: (selectedLayerId) =>
        set({ selectedLayerId }, false, "setSelectedLayer"),

      // 그리기 관련 액션
      setDrawManager: (drawManager) =>
        set(
          {
            drawManager,
            isDrawManagerEventsSetup: false, // DrawManager 변경 시 이벤트 설정 플래그 초기화
          },
          false,
          "setDrawManager",
        ),

      initializeDrawManager: (options, measureOptions) => {
        const { map, drawManager } = get();
        if (!map) {
          console.warn(
            "Map is not initialized. Cannot initialize DrawManager.",
          );
          return;
        }

        // 이미 DrawManager가 있으면 중복 생성 방지
        if (drawManager) {
          console.warn("DrawManager is already initialized.");
          return;
        }

        // 초기화 중 플래그 설정하여 중복 호출 방지
        if (get().isInitializingDrawManager) {
          console.warn("DrawManager initialization already in progress.");
          return;
        }

        set({ isInitializingDrawManager: true }, false, "setInitializingFlag");

        try {
          const newDrawManager = Draw.getInstance();
          newDrawManager.initialize(map, options, measureOptions);

          // 초기화 성공 후 상태 업데이트
          set(
            {
              drawManager: newDrawManager,
              isInitializingDrawManager: false,
            },
            false,
            "initializeDrawManager",
          );

          // 이벤트 리스너는 별도로 등록
          get().setupDrawManagerEvents(newDrawManager);

          console.log("DrawManager initialized successfully");
        } catch (error) {
          console.error("Failed to initialize DrawManager:", error);
          const errorMessage =
            error instanceof Error ? error.message : String(error);
          set(
            {
              error: errorMessage,
              isInitializingDrawManager: false,
            },
            false,
            "initializeDrawManagerError",
          );
        }
      },

      setupDrawManagerEvents: (drawManager) => {
        console.log("setupDrawManagerEvents 시작");

        // 이미 이벤트 리스너가 설정되었는지 확인
        const { isDrawManagerEventsSetup } = get();
        if (isDrawManagerEventsSetup) {
          console.warn("DrawManager events already setup, skipping...");
          return;
        }

        // DrawManager 이벤트 리스너 등록 (Stateless)
        drawManager.addEventListener("drawend", (feature) => {
          console.log("그리기 drawend 이벤트 감지됨", feature);
          // 안전한 ID 생성
          let featureId: string;
          try {
            featureId =
              feature?.getId?.() ||
              `drawing_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
          } catch (error) {
            console.warn(
              "Failed to get feature ID, generating fallback:",
              error,
            );
            featureId = `drawing_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
          }

          // 현재 React store의 drawingMode 사용 (stateless)
          const currentState = get();
          const drawingFeature: DrawingFeature = {
            id: featureId,
            type: currentState.drawingMode, // React store에서 관리되는 상태 사용
            coordinates: feature?.getGeometry?.()?.getCoordinates?.() || [],
            geometry: feature?.getGeometry?.() || null,
            properties: feature?.getProperties?.() || {},
            createdAt: new Date(),
          };

          set(
            (state) => ({
              features: [...state.features, drawingFeature],
              drawingMode: null, // 그리기 완료 후 drawingMode를 null로 설정
            }),
            false,
            "addFeature",
          );
        });

        // 통합된 컨텍스트 메뉴 이벤트 리스너 등록 (그리기 및 측정 레이어 모두 지원)
        drawManager.setupUnifiedContextMenuListener(
          (feature, coordinates, position, layerType) => {
            console.log(
              "통합 contextmenu 이벤트 감지됨",
              feature,
              coordinates,
              position,
              layerType,
            );
            const { setSelectedFeature, setContextMenuInfo } = get();

            if (feature) {
              // Feature를 사용하여 객체 생성 (layerType 포함)
              const selectedFeature = Feature.createSelectedFeature(feature);

              // layerType을 selectedFeature에 직접 설정
              if (layerType) {
                selectedFeature.layerType = layerType;
              }

              const contextMenuInfo = Feature.createContextMenuInfo(
                feature,
                coordinates,
                position,
                layerType,
              );

              setSelectedFeature(selectedFeature);
              setContextMenuInfo(contextMenuInfo);
            } else {
              setSelectedFeature(null);
              setContextMenuInfo({
                visible: false,
                position: [0, 0],
                feature: null,
                pixel: [0, 0],
              });
            }
          },
        );

        // 측정 이벤트 리스너 등록 (measureControl에 직접 등록)
        try {
          const measureManager = drawManager.getMeasureManager();
          const measureControl = drawManager.getMeasureControl();

          if (measureManager && measureControl) {
            // measureControl에 직접 drawend 이벤트 리스너 등록
            const measureDrawEndListenerId = measureManager.addEventListener(
              "drawend",
              (feature: any) => {
                console.log("측정 drawend 이벤트 감지됨", feature);

                // 현재 상태 확인
                const currentState = get();
                if (
                  !currentState.drawingMode ||
                  !currentState.drawingMode.startsWith("measure-")
                ) {
                  console.log("측정 모드가 아니므로 무시");
                  return;
                }

                // MeasureFeature로 변환
                const measureFeature = measureManager.convertToMeasureFeature(
                  feature,
                  currentState.drawingMode as any,
                );

                console.log("측정 완료, feature 추가:", measureFeature);

                // 상태 업데이트
                set(
                  (state) => ({
                    features: [...state.features, measureFeature],
                    drawingMode: null, // 측정 완료 후 drawingMode를 null로 설정
                  }),
                  false,
                  "addMeasureFeature",
                );
              },
            );

            // 이벤트 리스너 ID를 drawManager에 저장 (cleanup을 위해)
            drawManager.addEventListenerId(
              "measure_drawend",
              measureDrawEndListenerId,
            );
          }
        } catch (error) {
          console.warn("Failed to add measure event listener:", error);
          // 측정 이벤트 리스너 등록에 실패해도 앱이 중단되지 않도록 함
        }

        // 이벤트 리스너 설정 완료 플래그 설정
        set(
          { isDrawManagerEventsSetup: true },
          false,
          "setDrawManagerEventsSetup",
        );
        console.log("setupDrawManagerEvents 완료");
      },

      addFeature: (feature) => {
        const { saveStateToHistory } = get();
        saveStateToHistory(); // 현재 상태를 히스토리에 저장
        set(
          (state) => ({ features: [...state.features, feature] }),
          false,
          "addFeature",
        );
      },
      removeFeature: (featureId) => {
        const { saveStateToHistory } = get();
        saveStateToHistory(); // 현재 상태를 히스토리에 저장
        set(
          (state) => ({
            features: state.features.filter(
              (feature) => feature.id !== featureId,
            ),
          }),
          false,
          "removeFeature",
        );
      },
      setDrawingMode: (drawingMode) =>
        set({ drawingMode }, false, "setDrawingMode"),
      setFeatures: (features) => set({ features }, false, "setFeatures"),
      clearFeatures: () => {
        const { drawManager, saveStateToHistory } = get();
        saveStateToHistory(); // 현재 상태를 히스토리에 저장
        if (drawManager) {
          drawManager.clear();
        }
        set({ features: [] }, false, "clearFeatures");
      },

      startDrawing: (mode) => {
        const { drawManager } = get();
        if (!drawManager) {
          console.warn("DrawManager is not initialized. Cannot start drawing.");
          return;
        }

        drawManager.startDrawing(mode);
        set({ drawingMode: mode }, false, "startDrawing");
      },

      stopDrawing: () => {
        const { drawManager } = get();
        if (drawManager) {
          drawManager.stopDrawing();
        }
        set({ drawingMode: null }, false, "stopDrawing");
      },

      // 측정 관련 액션
      startMeasuring: (mode) => {
        const { drawManager } = get();
        if (!drawManager) {
          console.warn(
            "DrawManager is not initialized. Cannot start measuring.",
          );
          return;
        }

        drawManager.startDrawing(mode); // Draw 클래스에서 측정 모드도 처리
        set({ drawingMode: mode }, false, "startMeasuring");
      },

      stopMeasuring: () => {
        const { drawManager } = get();
        if (drawManager) {
          drawManager.stopDrawing(); // Draw 클래스에서 측정 중지도 처리
        }
        set({ drawingMode: null }, false, "stopMeasuring");
      },

      addMeasureFeature: (feature) => {
        const { saveStateToHistory } = get();
        saveStateToHistory(); // 현재 상태를 히스토리에 저장
        set(
          (state) => ({ features: [...state.features, feature] }),
          false,
          "addMeasureFeature",
        );
      },

      // 선택된 feature 관련 액션
      setSelectedFeature: (selectedFeature) =>
        set({ selectedFeature }, false, "setSelectedFeature"),

      setContextMenuInfo: (contextMenuInfo) =>
        set({ contextMenuInfo }, false, "setContextMenuInfo"),

      deleteSelectedFeature: () => {
        const { drawManager, selectedFeature } = get();
        if (!drawManager || !selectedFeature) return;

        let success = false;

        console.log("selectedFeature", selectedFeature);

        // 측정 feature인지 확인
        if (selectedFeature.layerType === "measure") {
          // 측정 레이어에서 삭제
          const measureManager = drawManager.getMeasureManager();
          if (measureManager) {
            const measureLayer = measureManager.getMeasureLayer();
            if (measureLayer) {
              // parent feature와 관련된 child feature들도 함께 삭제
              measureLayer.getFeatures().forEach((f: any) => {
                if (f.get("parent_ol_uid") === selectedFeature.feature.ol_uid) {
                  measureLayer.removeFeature(f);
                }
              });
              measureLayer.removeFeature(selectedFeature.feature);
              success = true;
            }
          }
        } else {
          // 일반 그리기 레이어에서 삭제
          const drawLayer = drawManager.getDrawLayer();
          success = Feature.deleteFeature(drawLayer, selectedFeature.feature);
        }

        if (success) {
          // features 배열에서도 제거
          set(
            (state) => ({
              features: state.features.filter(
                (feature) => feature.id !== selectedFeature.id,
              ),
              selectedFeature: null,
              contextMenuInfo: null,
            }),
            false,
            "deleteSelectedFeature",
          );
        }
      },

      changeSelectedFeatureStyle: (styleOptions) => {
        const { selectedFeature } = get();
        if (selectedFeature) {
          // Feature로 직접 스타일 변경
          const success = Feature.changeFeatureStyle(
            selectedFeature.feature,
            styleOptions,
          );
          if (success) {
            // 성공적으로 스타일이 변경되면 컨텍스트 메뉴 숨기기
            set({ contextMenuInfo: null }, false, "changeSelectedFeatureStyle");
          }
        }
      },

      // Undo/Redo 관련 액션
      saveStateToHistory: () => {
        const { features, undoStack } = get();
        const newUndoStack = [...undoStack, [...features]];

        // 최대 20개의 히스토리만 유지
        if (newUndoStack.length > 20) {
          newUndoStack.shift();
        }

        set(
          { undoStack: newUndoStack, redoStack: [] },
          false,
          "saveStateToHistory",
        );
      },

      undo: () => {
        const { undoStack, features, redoStack, drawManager } = get();

        if (undoStack.length === 0) return;

        const previousState = undoStack[undoStack.length - 1];
        const newUndoStack = undoStack.slice(0, -1);
        const newRedoStack = [...redoStack, [...features]];

        // DrawManager의 레이어도 업데이트
        if (drawManager) {
          drawManager.clear();
          // 이전 상태의 features를 다시 그리기 (실제 구현에서는 더 정교한 복원 필요)
        }

        set(
          {
            features: previousState,
            undoStack: newUndoStack,
            redoStack: newRedoStack,
            selectedFeature: null,
            contextMenuInfo: null,
          },
          false,
          "undo",
        );
      },

      redo: () => {
        const { redoStack, features, undoStack, drawManager } = get();

        if (redoStack.length === 0) return;

        const nextState = redoStack[redoStack.length - 1];
        const newRedoStack = redoStack.slice(0, -1);
        const newUndoStack = [...undoStack, [...features]];

        // DrawManager의 레이어도 업데이트
        if (drawManager) {
          drawManager.clear();
          // 다음 상태의 features를 다시 그리기 (실제 구현에서는 더 정교한 복원 필요)
        }

        set(
          {
            features: nextState,
            undoStack: newUndoStack,
            redoStack: newRedoStack,
            selectedFeature: null,
            contextMenuInfo: null,
          },
          false,
          "redo",
        );
      },

      canUndo: () => {
        const { undoStack } = get();
        return undoStack.length > 0;
      },

      canRedo: () => {
        const { redoStack } = get();
        return redoStack.length > 0;
      },

      // 마커 관련 액션
      addMarker: (marker) =>
        set(
          (state) => ({ markers: [...state.markers, marker] }),
          false,
          "addMarker",
        ),
      removeMarker: (markerId) =>
        set(
          (state) => ({
            markers: state.markers.filter((marker) => marker.id !== markerId),
          }),
          false,
          "removeMarker",
        ),
      updateMarker: (markerId, updates) =>
        set(
          (state) => ({
            markers: state.markers.map((marker) =>
              marker.id === markerId ? { ...marker, ...updates } : marker,
            ),
          }),
          false,
          "updateMarker",
        ),
      clearMarkers: () => set({ markers: [] }, false, "clearMarkers"),

      // 측정 관련 액션
      addMeasurement: (measurement) =>
        set(
          (state) => ({ measurements: [...state.measurements, measurement] }),
          false,
          "addMeasurement",
        ),
      removeMeasurement: (measurementId) =>
        set(
          (state) => ({
            measurements: state.measurements.filter(
              (measurement) => measurement.id !== measurementId,
            ),
          }),
          false,
          "removeMeasurement",
        ),
      clearMeasurements: () =>
        set({ measurements: [] }, false, "clearMeasurements"),

      // 전체 초기화
      reset: () => set(initialState, false, "reset"),
    }),
    {
      name: "map-store", // DevTools에서 표시될 이름
    },
  ),
);
