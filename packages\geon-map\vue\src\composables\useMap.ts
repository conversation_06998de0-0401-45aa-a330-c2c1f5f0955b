import { computed } from "vue";
import { useMapStore } from "../stores/mapStore";

/**
 * Vue composable for map functionality
 *
 * @example
 * ```vue
 * <script setup>
 * import { useMap } from '@geon-map/vue'
 *
 * const { map, isLoading, error, initializeMap } = useMap()
 *
 * onMounted(() => {
 *   if (mapContainer.value) {
 *     initializeMap(mapContainer.value)
 *   }
 * })
 * </script>
 * ```
 */
export function useMap() {
  // Nuxt.js 환경에서 안전하게 store 사용
  let mapStore: ReturnType<typeof useMapStore>;

  try {
    mapStore = useMapStore();
  } catch (error) {
    // SSR 환경이나 Pinia가 초기화되지 않은 경우 처리
    console.warn("MapStore not available:", error);
    return {
      map: computed(() => null),
      isLoading: computed(() => false),
      error: computed(() => "Store not initialized"),
      initializeMap: () => {},
      destroyMap: () => {},
    };
  }

  // Computed properties for reactive access
  const map = computed(() => mapStore.map);
  const isLoading = computed(() => mapStore.isLoading);
  const error = computed(() => mapStore.error);

  // Actions
  const initializeMap = async (container: HTMLElement, options?: any) => {
    await mapStore.initializeMap(container, options);
  };

  const destroyMap = () => {
    mapStore.destroyMap();
  };

  return {
    // State
    map,
    isLoading,
    error,

    // Actions
    initializeMap,
    destroyMap,
  };
}
