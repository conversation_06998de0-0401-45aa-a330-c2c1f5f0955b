"use client";

import React, { createContext, useEffect, useMemo } from "react";

import { useMapSetup } from "../hooks/use-map-setup";
import type { MapInitializeOptions, UseMapReturn } from "../types/map-types";

export const MapContext = createContext<UseMapReturn | null>(null);

// MapProvider Props - MapInitializeOptions를 직접 확장
export interface MapProviderProps extends MapInitializeOptions {
  children: React.ReactNode;
  containerRef?: React.RefObject<HTMLDivElement>;
  autoInit?: boolean;
  onMapInit?: (mapState: UseMapReturn) => void;
}

/**
 * Map Provider
 *
 * 지도 초기화 옵션을 전역으로 설정하고 MapContext를 제공합니다.
 * Map 컴포넌트와 함께 사용해야 합니다.
 *
 * @example
 * ```tsx
 * <MapProvider projection="EPSG:4326" zoom={10}>
 *   <Map className="w-full h-96" />
 *   <AddressSearchWidget />
 * </MapProvider>
 * ```
 */
export function MapProvider({
  children,
  containerRef,
  autoInit = false,
  onMapInit,
  ...mapInitializeOptions
}: MapProviderProps) {
  // 임시 containerRef 생성 (Map 컴포넌트에서 실제 ref를 제공할 때까지)
  const tempContainerRef = React.useRef<HTMLDivElement>(null);
  const actualContainerRef = containerRef || tempContainerRef;

  const mapState = useMapSetup({
    containerRef: actualContainerRef as React.RefObject<HTMLDivElement>,
    autoInit,
    ...mapInitializeOptions,
  });

  const contextValue = useMemo(
    () => mapState,
    [mapState.map, mapState.isLoading],
  );

  // 지도 초기화 콜백
  useEffect(() => {
    if (mapState.map && onMapInit) {
      onMapInit(mapState);
    }
  }, [onMapInit, mapState.map]);

  return (
    <MapContext.Provider value={contextValue}>{children}</MapContext.Provider>
  );
}
