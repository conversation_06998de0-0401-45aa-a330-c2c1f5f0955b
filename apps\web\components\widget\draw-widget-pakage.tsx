"use client";

import { useDraw } from "@geon-map/react-odf";
import React from "react";

import { DrawContextMenu } from "./draw-context-menu";
import { DrawWidget, MapStatusWidget } from "./draw-widget";
import { HeadlessDrawingWidget } from "./headless-drawing-widget";

export default function DrawWidgetPakage() {
  // 일반적으로 상위 컴포넌트에서 호출한 뒤 props로 하위 컴포넌트에 전달하는 방식을 권장
  const {
    startDrawing,
    stopDrawing,
    clearAllFeatures,
    features,
    drawingMode,
    isDrawing,
    importFromGeoJSON,
    selectedFeature,
    canUndo,
    canRedo,
  } = useDraw();

  return (
    <>
      {/* 지도 */}

      {/* 현대적인 헤드리스 그리기 위젯 */}
      <HeadlessDrawingWidget />

      {/* 기존 그리기 위젯 - props로 상태와 액션 전달 */}
      <DrawWidget
        startDrawing={startDrawing}
        stopDrawing={stopDrawing}
        clearAllFeatures={clearAllFeatures}
        features={features}
        drawingMode={drawingMode}
        isDrawing={isDrawing}
        importFromGeoJSON={importFromGeoJSON}
      />

      <DrawContextMenu />

      {/* 상태 표시 위젯 - props로 상태 전달 */}
      <MapStatusWidget
        features={features}
        drawingMode={drawingMode}
        isDrawing={isDrawing}
        selectedFeature={selectedFeature}
        canUndo={canUndo}
        canRedo={canRedo}
      />
    </>
  );
}
