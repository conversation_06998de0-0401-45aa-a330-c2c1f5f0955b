"use client";

import { DrawingMode } from "@geon-map/core";
import { useDraw } from "@geon-map/react-odf";
import React from "react";

interface DrawWidgetProps {
  startDrawing: (mode: DrawingMode) => void;
  stopDrawing: () => void;
  clearAllFeatures: () => void;
  features: unknown[];
  drawingMode: string | null;
  isDrawing: boolean;
  importFromGeoJSON: (geojson: object) => void;
}

interface MapStatusWidgetProps {
  features: unknown[];
  drawingMode: string | null;
  isDrawing: boolean;
  selectedFeature?: any;
  canUndo?: boolean;
  canRedo?: boolean;
}

export const testGeoJSON = {
  type: "FeatureCollection",
  features: [
    {
      type: "Feature",
      geometry: {
        type: "LineString",
        coordinates: [
          [955875.860988876, 1953003.2280330302],
          [955467.860988876, 1952895.1080330303],
        ],
      },
      properties: {},
    },
    {
      type: "Feature",
      geometry: {
        type: "Polygon",
        coordinates: [
          [
            [955238.5556560195, 1953240.6754137075],
            [955132.4756560194, 1953128.4754137076],
            [955259.9756560194, 1953067.2754137076],
            [955238.5556560195, 1953240.6754137075],
          ],
        ],
      },
      properties: {},
    },
  ],
  odfId: "odf-layer-draw-unique",
};

/**
 * 그리기 위젯 컴포넌트
 *
 * Props를 통해 상태와 액션을 받는 헤드리스 컴포넌트 패턴
 */
export function DrawWidget({
  startDrawing,
  stopDrawing,
  clearAllFeatures,
  features,
  drawingMode,
  isDrawing,
  importFromGeoJSON,
}: DrawWidgetProps) {
  // 새로운 기능들을 위해 useDraw 훅 사용
  const {
    selectedFeature,
    handleDeleteSelectedFeature,
    handleUndo,
    handleRedo,
    canUndo,
    canRedo,
  } = useDraw();
  const drawingTools = [
    {
      id: "lineString",
      label: "직선",
      emoji: "📏",
      className: "bg-blue-50 hover:bg-blue-100 border-blue-200 text-blue-700",
    },
    {
      id: "polygon",
      label: "다각형",
      emoji: "⬟",
      className:
        "bg-green-50 hover:bg-green-100 border-green-200 text-green-700",
    },
    {
      id: "point",
      label: "점",
      emoji: "📍",
      className:
        "bg-purple-50 hover:bg-purple-100 border-purple-200 text-purple-700",
    },
    {
      id: "circle",
      label: "원",
      emoji: "⭕",
      className:
        "bg-orange-50 hover:bg-orange-100 border-orange-200 text-orange-700",
    },
    {
      id: "text",
      label: "텍스트",
      emoji: "📝",
      className:
        "bg-indigo-50 hover:bg-indigo-100 border-indigo-200 text-indigo-700",
    },
  ];

  const measureTools = [
    {
      id: "measure-distance",
      label: "거리측정",
      emoji: "📐",
      className:
        "bg-yellow-50 hover:bg-yellow-100 border-yellow-200 text-yellow-700",
    },
    {
      id: "measure-area",
      label: "면적측정",
      emoji: "📊",
      className: "bg-pink-50 hover:bg-pink-100 border-pink-200 text-pink-700",
    },
    {
      id: "measure-round",
      label: "원형측정",
      emoji: "🎯",
      className: "bg-cyan-50 hover:bg-cyan-100 border-cyan-200 text-cyan-700",
    },
    {
      id: "measure-spot",
      label: "지점측정",
      emoji: "📍",
      className: "bg-teal-50 hover:bg-teal-100 border-teal-200 text-teal-700",
    },
  ];

  const handleToolClick = (toolId: string) => {
    if (drawingMode === toolId) {
      stopDrawing();
    } else {
      startDrawing(toolId as DrawingMode);
    }
  };

  const handleMeasureToolClick = (toolId: string) => {
    if (drawingMode === toolId) {
      stopDrawing();
    } else {
      // toolId는 이미 "measure-distance" 형태이므로 그대로 startDrawing에 전달
      startDrawing(toolId as DrawingMode);
    }
  };

  return (
    <div className="absolute left-4 top-4 z-10 w-64">
      <div className="rounded-lg border bg-white shadow-lg">
        <div className="border-b p-4">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-gray-900">그리기 & 측정 도구</h3>
            <div className="flex items-center gap-2">
              <span className="rounded-full bg-gray-100 px-2 py-1 text-xs text-gray-600">
                {features.length}개
              </span>
              {isDrawing && (
                <div className="flex items-center gap-1">
                  <div className="h-2 w-2 animate-pulse rounded-full bg-green-500"></div>
                  <span className="text-xs text-green-600">
                    {drawingMode?.startsWith("measure-")
                      ? "측정 중"
                      : "그리기 중"}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="p-4">
          {/* 그리기 도구 버튼들 */}
          <div className="mb-4">
            <h4 className="mb-2 text-sm font-medium text-gray-700">
              그리기 도구
            </h4>
            <div className="grid grid-cols-2 gap-2">
              {drawingTools.map((tool) => (
                <button
                  key={tool.id}
                  onClick={() => handleToolClick(tool.id)}
                  className={`flex items-center gap-2 rounded-md border px-3 py-2 text-sm font-medium transition-colors ${
                    drawingMode === tool.id
                      ? "ring-2 ring-blue-500 ring-offset-1"
                      : ""
                  } ${tool.className} `}
                >
                  <span className="text-base">{tool.emoji}</span>
                  {tool.label}
                  {drawingMode === tool.id && (
                    <span className="ml-auto text-xs">●</span>
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* 측정 도구 버튼들 */}
          <div className="mb-4">
            <h4 className="mb-2 text-sm font-medium text-gray-700">
              측정 도구
            </h4>
            <div className="grid grid-cols-2 gap-2">
              {measureTools.map((tool) => (
                <button
                  key={tool.id}
                  onClick={() => handleMeasureToolClick(tool.id)}
                  className={`flex items-center gap-2 rounded-md border px-3 py-2 text-sm font-medium transition-colors ${
                    drawingMode === tool.id
                      ? "ring-2 ring-orange-500 ring-offset-1"
                      : ""
                  } ${tool.className} `}
                >
                  <span className="text-base">{tool.emoji}</span>
                  {tool.label}
                  {drawingMode === tool.id && (
                    <span className="ml-auto text-xs">●</span>
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* 제어 버튼들 */}
          <div className="space-y-2">
            <button
              onClick={() => {
                stopDrawing();
              }}
              disabled={!drawingMode}
              className="w-full rounded-md bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200 disabled:cursor-not-allowed disabled:opacity-50"
            >
              🛑{" "}
              {drawingMode?.startsWith("measure-")
                ? "측정 중지"
                : "그리기 중지"}
            </button>
          </div>

          {/* Undo/Redo 버튼들 */}
          <div className="mb-4 grid grid-cols-2 gap-2">
            <button
              onClick={handleUndo}
              disabled={!canUndo}
              className="flex items-center justify-center gap-2 rounded-md bg-gray-50 px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 disabled:cursor-not-allowed disabled:opacity-50"
            >
              ↶ 실행취소
            </button>
            <button
              onClick={handleRedo}
              disabled={!canRedo}
              className="flex items-center justify-center gap-2 rounded-md bg-gray-50 px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 disabled:cursor-not-allowed disabled:opacity-50"
            >
              ↷ 다시실행
            </button>
          </div>

          {/* 선택된 feature 정보 */}
          {selectedFeature && (
            <div className="mb-4 rounded-md bg-blue-50 p-3">
              <div className="text-sm font-medium text-blue-900">
                선택된 도형: {selectedFeature.type}
              </div>
              <div className="mt-2 flex gap-2">
                <button
                  onClick={handleDeleteSelectedFeature}
                  className="rounded bg-red-500 px-2 py-1 text-xs text-white hover:bg-red-600"
                >
                  삭제
                </button>
              </div>
            </div>
          )}

          {/* 액션 버튼들 */}
          <div className="space-y-2">
            <button
              onClick={clearAllFeatures}
              disabled={features.length === 0}
              className="w-full rounded-md bg-red-50 px-3 py-2 text-sm font-medium text-red-700 hover:bg-red-100 disabled:cursor-not-allowed disabled:opacity-50"
            >
              🗑️ 모두 지우기
            </button>
            <button
              onClick={() => {
                importFromGeoJSON(testGeoJSON);
              }}
              className="w-full rounded-md bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200"
            >
              📥Test GeoJSON 불러오기
            </button>
          </div>
        </div>
      </div>

      {/* 컨텍스트 메뉴 - 이제 헤드리스 방식으로 자동으로 상태를 가져옴 */}
    </div>
  );
}

/**
 * 간단한 상태 표시 위젯
 */
export function MapStatusWidget({
  features,
  drawingMode,
  isDrawing,
  selectedFeature,
  canUndo = false,
  canRedo = false,
}: MapStatusWidgetProps) {
  return (
    <div className="absolute bottom-1 right-4 z-10 rounded-lg border bg-white/95 p-3 shadow-lg">
      <div className="space-y-1 text-sm">
        <div className="flex items-center justify-between">
          <span className="text-gray-600">그리기 수:</span>
          <span className="font-medium">{features.length}</span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-gray-600">모드:</span>
          <span className="font-medium">{drawingMode || "없음"}</span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-gray-600">상태:</span>
          <div className="flex items-center gap-1">
            <div
              className={`h-2 w-2 rounded-full ${
                isDrawing ? "animate-pulse bg-green-500" : "bg-gray-300"
              }`}
            />
            <span className="text-xs">{isDrawing ? "활성" : "대기"}</span>
          </div>
        </div>
        {selectedFeature && (
          <div className="flex items-center justify-between">
            <span className="text-gray-600">선택:</span>
            <span className="font-medium text-blue-600">
              {selectedFeature.type}
            </span>
          </div>
        )}
        <div className="flex items-center justify-between">
          <span className="text-gray-600">실행취소:</span>
          <span
            className={`text-xs ${canUndo ? "text-green-600" : "text-gray-400"}`}
          >
            {canUndo ? "가능" : "불가"}
          </span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-gray-600">다시실행:</span>
          <span
            className={`text-xs ${canRedo ? "text-green-600" : "text-gray-400"}`}
          >
            {canRedo ? "가능" : "불가"}
          </span>
        </div>
      </div>
    </div>
  );
}
