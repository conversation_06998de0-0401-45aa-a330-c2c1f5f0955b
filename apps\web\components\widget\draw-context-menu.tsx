import { Popup, useDrawContext } from "@geon-map/react-odf";
import React, { useEffect, useRef } from "react";

export const DrawContextMenu: React.FC = () => {
  const {
    selectedFeature,
    contextMenuInfo,
    handleDeleteSelectedFeature,
    handleStyleChange,
    handleCloseContextMenu,
  } = useDrawContext();

  const menuRef = useRef<HTMLDivElement>(null);

  // 메뉴 외부 클릭 시 닫기
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        handleCloseContextMenu();
      }
    };

    if (contextMenuInfo?.visible) {
      document.addEventListener("mousedown", handleClickOutside);
      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
      };
    }
  }, [contextMenuInfo?.visible, handleCloseContextMenu]);

  // ESC 키로 메뉴 닫기
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        handleCloseContextMenu();
      }
    };

    if (contextMenuInfo?.visible) {
      document.addEventListener("keydown", handleKeyDown);
      return () => {
        document.removeEventListener("keydown", handleKeyDown);
      };
    }
  }, [contextMenuInfo?.visible, handleCloseContextMenu]);

  // 스타일 변경 핸들러들
  const handleRedStyle = () => {
    handleStyleChange({
      fill: { color: [255, 0, 0, 0.6] },
      stroke: { color: [255, 0, 0, 0.8], width: 2 },
    });
  };

  const handleBlueStyle = () => {
    handleStyleChange({
      fill: { color: [0, 0, 255, 0.6] },
      stroke: { color: [0, 0, 255, 0.8], width: 2 },
    });
  };

  const handleGreenStyle = () => {
    handleStyleChange({
      fill: { color: [0, 255, 0, 0.6] },
      stroke: { color: [0, 255, 0, 0.8], width: 2 },
    });
  };

  return (
    <Popup
      visible={contextMenuInfo?.visible || false}
      position={contextMenuInfo?.position || [0, 0]}
      positioning="top-left"
      offset={[10, -10]}
      popupKey="draw-context-menu"
    >
      <div
        ref={menuRef}
        className="min-w-40 overflow-hidden rounded-lg border border-gray-300 bg-white shadow-lg"
      >
        <div className="border-b border-gray-200 px-3 py-2 text-xs font-medium text-gray-600">
          {selectedFeature?.type} 편집
        </div>

        {/* 스타일 섹션 */}
        <div className="py-1">
          <div className="px-3 py-1 text-xs font-bold uppercase text-gray-500">
            스타일
          </div>
          <button
            onClick={handleRedStyle}
            className="flex w-full items-center gap-2 px-3 py-2 text-left text-sm hover:bg-gray-100"
          >
            🔴 빨간색
          </button>
          <button
            onClick={handleBlueStyle}
            className="flex w-full items-center gap-2 px-3 py-2 text-left text-sm hover:bg-gray-100"
          >
            🔵 파란색
          </button>
          <button
            onClick={handleGreenStyle}
            className="flex w-full items-center gap-2 px-3 py-2 text-left text-sm hover:bg-gray-100"
          >
            🟢 초록색
          </button>
        </div>

        {/* 구분선 */}
        <div className="mx-2 h-px bg-gray-200" />

        {/* 액션 섹션 */}
        <div className="py-1">
          <button
            onClick={handleDeleteSelectedFeature}
            className="flex w-full items-center gap-2 px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50"
          >
            🗑️ 삭제
          </button>
        </div>
      </div>
    </Popup>
  );
};
