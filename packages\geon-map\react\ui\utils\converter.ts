import {
  AddressSearchResponse,
  BasemapListResponse,
  JusoItem,
} from "@geon-query/model";

import { BasemapMapType } from "../components";
import { AddressBase, AddressSearchTypes } from "../types";

export const basemapWidget = (result: BasemapListResponse): BasemapMapType => {
  try {
    if (result.code === 200) {
      return result.result.list.map((item: BasemapMapType[number]) => ({
        bcrnMapNm: item.bcrnMapNm,
        bcrnMapClCodeNm: item.bcrnMapClCodeNm,
        lyrStleCodeNm: item.lyrStleCodeNm,
        mapUrl: item.mapUrl,
        mapUrlparamtr: item.mapUrlparamtr,
        base64: item.base64,
      }));
    }
    return [];
  } catch (e) {
    console.error(e);
    return [];
  }
};

export const addressSearchWidget = ({
  result,
  searchType,
  srid,
}: {
  result: AddressSearchResponse;
  searchType: AddressSearchTypes;
  srid: number;
}): {
  resultList: AddressBase[];
  hasNextPage: boolean;
} => {
  try {
    if (result.code === 200) {
      const resultList = result.result.jusoList.map((item: JusoItem) => {
        const convertedItem: AddressBase = {
          srid: srid.toString(),
          roadAddress: item.roadAddr,
          jibunAddress: item.jibunAddr,
          buildingName: item.buildName,
          poiName: item.poiName,
        };

        /**
         * [결과 geometry 타입]
         * - Polygon(폴리곤) 표출:
         *    integrated, jibun, roadApi, road, building, postalCode, pnu
         * - Point(포인트) 표출:
         *    coordinates, poi
         */

        /**
         * [geometry 설정 우선순위]
         * - 통합/지번/도로명주소/건물명/기초구역번호(예:03070):
         *     1. 건물 폴리곤이 있으면 우선 사용
         *     2. 없으면 필지(토지) 폴리곤 사용
         * - pnu:
         *     필지(토지) 폴리곤만 표출
         * - poi:
         *     poi의 x, y 좌표만 표출
         * - 경위도 좌표:
         *     1. 건물 x, y가 있으면 우선 사용
         *     2. 없으면 필지 x, y 사용
         */

        // 검색 타입에 따른 geometry 설정
        if (searchType === "coordinates") {
          const useBuild = item.buildX && item.buildY;
          convertedItem.x = parseFloat(
            useBuild ? (item.buildX ?? "") : (item.parcelX ?? ""),
          );
          convertedItem.y = parseFloat(
            useBuild ? (item.buildY ?? "") : (item.parcelY ?? ""),
          );
        } else if (searchType === "poi") {
          convertedItem.x = parseFloat(item.poiX ?? "");
          convertedItem.y = parseFloat(item.poiY ?? "");
        } else if (searchType === "pnu") {
          // PNU는 필지 폴리곤만 표출
          convertedItem.geometryWKT = item.geom;
        } else {
          // 나머지는 건물 폴리곤 우선, 없으면 필지 폴리곤
          convertedItem.geometryWKT = item.buildGeom || item.geom;
        }
        return convertedItem;
      });
      return {
        resultList,
        hasNextPage:
          Number(result.result.common.totalCount) >
          Number(
            result.result.common.currentPage *
              result.result.common.countPerPage,
          ),
      };
    }
    return {
      resultList: [],
      hasNextPage: false,
    };
  } catch (e) {
    console.error(e);
    return {
      resultList: [],
      hasNextPage: false,
    };
  }
};
