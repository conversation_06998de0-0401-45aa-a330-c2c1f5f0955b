"use client";

import { useMouseCoordinate } from "@geon-map/react-odf";
import { useState } from "react";

export const MouseCoordDisplay = () => {
  const [enabled, setEnabled] = useState(false);
  const coordinate = useMouseCoordinate(enabled);

  return (
    <div className="w-fit rounded border p-2 text-sm">
      <button
        className="mb-2 rounded border bg-gray-100 px-3 py-1 hover:bg-gray-200"
        onClick={() => setEnabled((prev) => !prev)}
      >
        {enabled ? "좌표 표시 끄기" : "좌표 표시 켜기"}
      </button>

      {/* 좌표 표시 중일 때만 출력 */}
      {enabled && (
        <div>
          {coordinate ? (
            <>
              X: {coordinate.x}, Y: {coordinate.y}
            </>
          ) : (
            <>좌표를 불러오는 중...</>
          )}
        </div>
      )}
    </div>
  );
};
