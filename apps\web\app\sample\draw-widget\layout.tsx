import {
  DrawContextProvider,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Map<PERSON>rovider,
} from "@geon-map/react-odf";
import {
  SidebarInset,
  SidebarProvider,
} from "@geon-ui/react/primitives/sidebar";
import React from "react";

import { AppSidebar } from "@/components/sidebar/app-sidebar";
import InnerSidebar from "@/components/sidebar/inner-sidebar";
import {
  AppSidebarProvider,
  InnerSidebarProvider,
} from "@/contexts/app-sidebar";

export default function WidgetLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <SidebarProvider>
      <AppSidebarProvider>
        <AppSidebar />
        <MapProvider projection="EPSG:5186" zoom={10}>
          <LayerProvider>
            <DrawProvider
              continuity={false}
              style={{
                fill: { color: [255, 0, 0, 1] },
                stroke: { color: [0, 255, 0, 0.8], width: 5 },
              }}
              measureOptions={{
                continuity: false,
                style: {
                  fill: { color: [0, 100, 0, 0.3] },
                  stroke: { color: [0, 100, 0, 1], width: 3 },
                },
              }}
            >
              <DrawContextProvider>
                <InnerSidebarProvider>
                  <InnerSidebar />
                  <SidebarInset className="overflow-hidden">
                    {children}
                  </SidebarInset>
                </InnerSidebarProvider>
              </DrawContextProvider>
            </DrawProvider>
          </LayerProvider>
        </MapProvider>
      </AppSidebarProvider>
    </SidebarProvider>
  );
}
