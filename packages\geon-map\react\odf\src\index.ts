"use client";

// Components
export { Layer } from "./components/layer";
export { Map } from "./components/map";
export { Marker } from "./components/marker";
export { Popup } from "./components/popup";
export { Style } from "./components/style";
// DrawContextMenu는 앱별로 커스터마이징이 필요하므로 제거
// 대신 useDrawContext 훅을 통해 헤드리스 방식으로 제공

// Hooks
export { useDraw } from "./hooks/use-draw";
export { useMouseCoordinate } from "./hooks/use-event";
export { useFeature } from "./hooks/use-feature";
export { useLayer } from "./hooks/use-layer";
export { useMap } from "./hooks/use-map";
export { useMapSetup } from "./hooks/use-map-setup";

// Draw Types
export type {
  ContextMenuInfo,
  Drawing,
  DrawingFeature,
  MeasureFeature,
  MeasureMode,
  SelectedFeature,
} from "./hooks/use-draw";

// Contexts - 개별 Provider들
export type {
  DrawContextProviderProps,
  DrawContextValue,
  DrawProviderProps,
} from "./contexts/draw-context";
export {
  DrawContextProvider,
  DrawProvider,
  useDrawContext,
} from "./contexts/draw-context";
export { LayerProvider, useLayerContext } from "./contexts/layer-context";
export type { MapProviderProps } from "./contexts/map-context";
export { MapProvider } from "./contexts/map-context";

// Store
export { useMapStore } from "./stores/map-store";

// Types
export type * from "./types/layer-types";
export type * from "./types/map-types";
export type * from "./types/marker-types";
export type * from "./types/popup-types";

// Explicit exports for commonly used types
export type { BasemapId } from "./types/map-types";
