export type API_TYPE =
  | "map"
  | "addrgeo"
  | "analysis"
  | "coord"
  | "publish"
  | "smt"
  | "estate";
export const BASE_URL = "https://city.geon.kr/api/";

export const crtfckey = "UxizIdSqCePz93ViFt8ghZFFJuOzvUp0";

interface ApiUrlParams {
  endpoint: string;
  type: API_TYPE;
  baseUrl?: string;
  apiKey?: string;
}
/**
 * GEON API URL을 생성하는 함수
 * @param {ApiUrlParams} params - API URL 생성 파라미터
 * @returns {string} 완성된 API URL
 * @example
 * ```typescript
 * // 기본 사용
 * const url1 = apiUrl({ endpoint: "/search", type: "addrgeo" });
 * // → "https://city.geon.kr/api/addrgeo/search?crtfckey=UxizIdSqCePz93ViFt8ghZFFJuOzvUp0"
 *
 * // 커스텀 설정 사용
 * const url2 = apiUrl({
 *   endpoint: "/address/bld",
 *   type: "addrgeo",
 *   baseUrl: "https://dev.geon.kr/api/",
 *   apiKey: "custom-key"
 * });
 * // → "https://dev.geon.kr/api/addrgeo/address/bld?crtfckey=custom-key"
 * ```
 */
export const apiUrl = ({
  endpoint,
  type,
  baseUrl = BASE_URL,
  apiKey = crtfckey,
}: ApiUrlParams): string => `${baseUrl}${type}${endpoint}?crtfckey=${apiKey}`;
