// event-types.ts

export type MapEventType =
  | "change"
  | "change:layerGroup"
  | "change:size"
  | "change:target"
  | "change:view"
  | "click"
  | "dblclick"
  | "moveend"
  | "movestart"
  | "pointerdrag"
  | "pointermove"
  | "precompose"
  | "postcompose"
  | "postrender"
  | "propertychange"
  | "singleclick"
  | "contextmenu"
  | "modifyend"
  | "editend"
  | "editstart";

export type ViewEventType =
  | "change"
  | "change:center"
  | "change:resolution"
  | "change:rotation"
  | "propertychange";

export type LayerEventType =
  | "change"
  | "change:extent"
  | "change:maxResolution"
  | "change:minResolution"
  | "change:opacity"
  | "change:visible"
  | "change:zIndex"
  | "propertychange"
  | "precompose"
  | "prerender"
  | "postcompose"
  | "postrender"
  | "rendercomplete"
  | "change:blur"
  | "change:gradient"
  | "change:radius"
  | "change:source"
  | "change:preload"
  | "change:useInterimTilesOnError";

export type SourceEventType =
  | "change"
  | "propertychange"
  | "imageloadend"
  | "imageloaderror"
  | "imageloadstart"
  | "afteroperations"
  | "beforeoperations"
  | "tileloadstart"
  | "tileloadend"
  | "tileloaderror"
  | "addfeature"
  | "featureloadend"
  | "changefeature"
  | "clear"
  | "removefeature";

export type MarkerEventType =
  | "change"
  | "change:position"
  | "change:draggable"
  | "change:map"
  | "click"
  | "markerdragstart"
  | "markerdrag"
  | "markerdragend";

export type DrawControlEventType = "drawstart" | "drawend";

// 전체 이벤트를 하나로 묶은 유니언 타입도 제공 (필요시)
export type MapSystemEventType =
  | MapEventType
  | ViewEventType
  | LayerEventType
  | SourceEventType
  | MarkerEventType
  | DrawControlEventType;

export type OneTimeEventOption = boolean | "customize" | "odfDeleted";
