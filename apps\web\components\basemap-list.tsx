"use client";

import { defaultGeonSmtClient } from "@geon-query/model/restapi/smt";
import type { BasemapListRequest } from "@geon-query/model/restapi/type/smt-type";
import { useAppQuery } from "@geon-query/react-query";
import { useState } from "react";

export default function BasemapList() {
  const [params, setParams] = useState<BasemapListRequest>({
    pageIndex: 1,
    pageSize: 100,
    // 필요시 추가: bcrnMapNm: "기본지도"
  });
  console.log(setParams);

  const { data, isLoading, isFetching, refetch, error } = useAppQuery({
    queryKey: ["basemapList", params],
    queryFn: () => defaultGeonSmtClient.basemap.list(params),
  });

  return (
    <div style={{ padding: 16 }}>
      <h2>🗺️ 베이스맵 목록</h2>

      <button onClick={() => refetch()} disabled={isFetching}>
        🔄 목록 새로고침
      </button>

      {isLoading || isFetching ? <p>불러오는 중...</p> : null}

      {error instanceof Error && (
        <p style={{ color: "red" }}>에러: {error.message}</p>
      )}

      <ul style={{ marginTop: 16 }}>
        {data?.result?.list?.map(
          (
            item: { bcrnMapClCodeNm: string; bcrnMapId: string },
            index: number,
          ) => (
            <li key={index}>
              <strong>{item.bcrnMapClCodeNm}</strong> ({item.bcrnMapId})
            </li>
          ),
        ) ?? <p>목록이 없습니다.</p>}
      </ul>
    </div>
  );
}
