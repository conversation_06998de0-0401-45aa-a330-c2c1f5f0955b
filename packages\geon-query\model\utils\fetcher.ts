import { Log } from "./Log";

export const fetcher = {
  get: async <T = unknown>(
    url: string,
    params?: Record<string, any>,
  ): Promise<T> => {
    const queryString = params
      ? Object.entries(params)
          .map(
            ([key, value]) =>
              `${encodeURIComponent(key)}=${encodeURIComponent(value)}`,
          )
          .join("&")
      : "";

    const fullUrl = queryString
      ? url.includes("?")
        ? `${url}&${queryString}`
        : `${url}?${queryString}`
      : url;
    Log.logRequest("GET", fullUrl);

    try {
      const res = await fetch(fullUrl);
      const data = await res.json();
      Log.logResponse("GET", fullUrl, data);
      return data;
    } catch (error) {
      Log.logError("GET", fullUrl, error);
      throw error;
    }
  },

  post: async <T = unknown, B = unknown>(url: string, body: B): Promise<T> => {
    Log.logRequest("POST", url, body);
    try {
      const res = await fetch(url, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(body),
      });
      const data = await res.json();
      Log.logResponse("POST", url, data);
      return data;
    } catch (error) {
      Log.logError("POST", url, error);
      throw error;
    }
  },
};
