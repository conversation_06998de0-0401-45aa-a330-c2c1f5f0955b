import { create } from "zustand";
import { devtools } from "zustand/middleware";

import type { Layer } from "../types/layer-types";

// 레이어 상태 인터페이스
export interface LayerState {
  // 레이어 목록
  layers: Layer[];
  selectedLayerId?: string;
  expandedGroups: Set<string>;

  // 액션들
  addLayer: (layer: Layer) => void;
  removeLayer: (layerId: string) => void;
  updateLayer: (id: string, updates: Partial<Layer>) => void;
  setLayers: (layers: Layer[]) => void;
  toggleLayerVisibility: (layerId: string) => void;
  setLayerFilter: (id: string, filter: string) => void;
  setSelectedLayer: (layerId: string) => void;
  toggleGroup: (groupId: string) => void;
  clearLayers: () => void;
}

// Zustand store 생성
export const useLayerStore = create<LayerState>()(
  devtools(
    (set) => ({
      // 초기 상태
      layers: [],
      selectedLayerId: undefined,
      expandedGroups: new Set(),

      // 액션 구현
      addLayer: (layer: Layer) =>
        set(
          (state) => ({
            layers: [...state.layers, layer],
          }),
          false,
          "addLayer",
        ),

      removeLayer: (layerId: string) =>
        set(
          (state) => ({
            layers: state.layers.filter((layer) => layer.id !== layerId),
            selectedLayerId:
              state.selectedLayerId === layerId
                ? undefined
                : state.selectedLayerId,
          }),
          false,
          "removeLayer",
        ),

      updateLayer: (id: string, updates: Partial<Layer>) =>
        set(
          (state) => ({
            layers: state.layers.map((layer) =>
              layer.id === id ? { ...layer, ...updates } : layer,
            ),
          }),
          false,
          "updateLayer",
        ),

      setLayers: (layers: Layer[]) =>
        set(
          () => ({
            layers,
          }),
          false,
          "setLayers",
        ),

      toggleLayerVisibility: (layerId: string) =>
        set(
          (state) => ({
            layers: state.layers.map((layer) =>
              layer.id === layerId
                ? { ...layer, visible: !layer.visible }
                : layer,
            ),
          }),
          false,
          "toggleLayerVisibility",
        ),

      setLayerFilter: (id: string, filter: string) =>
        set(
          (state) => ({
            layers: state.layers.map((layer) =>
              layer.id === id ? { ...layer, filter } : layer,
            ),
          }),
          false,
          "setLayerFilter",
        ),

      setSelectedLayer: (layerId: string) =>
        set(
          () => ({
            selectedLayerId: layerId,
          }),
          false,
          "setSelectedLayer",
        ),

      toggleGroup: (groupId: string) =>
        set(
          (state) => {
            const newExpandedGroups = new Set(state.expandedGroups);
            if (newExpandedGroups.has(groupId)) {
              newExpandedGroups.delete(groupId);
            } else {
              newExpandedGroups.add(groupId);
            }
            return {
              expandedGroups: newExpandedGroups,
            };
          },
          false,
          "toggleGroup",
        ),

      clearLayers: () =>
        set(
          () => ({
            layers: [],
            selectedLayerId: undefined,
            expandedGroups: new Set(),
          }),
          false,
          "clearLayers",
        ),
    }),
    {
      name: "layer-store",
    },
  ),
);
