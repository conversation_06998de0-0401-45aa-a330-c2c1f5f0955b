// packages/query/reactQuery5/utils/Log.ts
export class Log {
  private static EXCLUDED_URLS = ['selectRainFallRegDate', 'xxx'];

  static logRequest(method: string, url: string, data?: unknown) {
    if (this.isExcluded(url)) return;

    const logs = [
      `%c[REQ]%c %c[${method.toUpperCase()}] %c${url}`,
      'color:white; background:skyblue;',
      '',
      'color: red',
      'color: black',
      data,
    ];
    console.log(...logs);
  }

  static logResponse(method: string, url: string, res: unknown) {
    if (this.isExcluded(url)) return;

    const logs = [
      `%c[RES]%c %c[${method.toUpperCase()}] %c${url}`,
      'color:white; background:blue;',
      '',
      'color: red',
      'color: black',
      res,
    ];
    console.log(...logs);
  }

  static logError(method: string, url: string, error: unknown) {
    const logs = [
      `%c[REQ]%c %c[ERROR] %c${url}`,
      'color:white; background:blue;',
      '',
      'color:red',
      'color:red',
      error,
    ];
    console.error(...logs);
  }

  private static isExcluded(url: string) {
    return this.EXCLUDED_URLS.some(ex => url.includes(ex));
  }
}
