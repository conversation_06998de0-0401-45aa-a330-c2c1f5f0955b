type WKTPolygonType = `POLYGON(${string})` | `MULTIPOLYGON(${string})`;

/** 공통: 인증키와 키워드 */
export interface AddressBaseRequest {
  keyword: string;
  crtfckey?: string;
  currentPage?: number;
  countPerPage?: number;
}

/** 공통: 좌표계/중복결과 */
export interface AddressAdvancedRequest {
  targetSrid?: number;
  showMultipleResults?: boolean;
}

/** 건물 주소 검색 요청 */
export interface AddressBldRequest extends AddressBaseRequest {
  targetSrid?: number;
}

/** 통합 주소 검색 요청 */
export interface AddressIntRequest
  extends AddressBaseRequest,
    AddressAdvancedRequest {}

/** 지번 주소 검색 요청 */
export interface AddressJibunRequest
  extends AddressBaseRequest,
    AddressAdvancedRequest {}

/** 도로명 주소 검색 요청 */
export interface AddressRoadRequest
  extends AddressBaseRequest,
    AddressAdvancedRequest {}

/** 도로 링크 기반 주소 검색 요청 */
export interface AddressRoadLinkRequest extends AddressBaseRequest {
  targetSrid?: number;
}

/** 좌표 → 주소 변환 요청 (구조 다름) */
export interface AddressCoordRequest {
  lat: number;
  lng: number;
  crtfckey?: string;
  showMultipleResults?: boolean;
  targetSrid?: number;
  byPass?: boolean;
}

/** PNU 기반 주소 조회 요청 */
export interface AddressPnuRequest
  extends AddressBaseRequest,
    AddressAdvancedRequest {}

/** POI 주소 검색 요청 */
export interface AddressPoiRequest extends AddressBaseRequest {
  targetSrid?: number;
  searchAddress: string;
}

/** 기초구역번호 검색 요청 */
export interface AddressBasicRequest extends AddressBaseRequest {}

// 공통 응답 구조
export interface AddressCommonResponse {
  searchAddressGbn: string;
  gbn: string;
  clOk: string;
  trOk: string;
  clCd: string;
  trCd: string;
  clMessage: string;
  trMessage: string;
  totalCount: string;
  currentPage: number;
  countPerPage: number;
}

// 주소 검색 결과 항목
export interface JusoItem {
  parcelX?: string;
  parcelY?: string;
  buildX?: string;
  buildY?: string;
  roadAddr?: string;
  jibunAddr?: string;
  addrPnu?: string;
  roadAddrIdKy?: string;
  roadBdMgNo?: string;
  geom?: WKTPolygonType;
  buildGeom?: WKTPolygonType;
  buildName?: string;
  parcelLo?: string;
  parcelLa?: string;
  buildLo?: string;
  buildLa?: string;
  poiName?: string;
  poiX?: string;
  poiY?: string;
}

export interface AddressSearchResponse {
  code: number;
  message: string;
  result: {
    common: AddressCommonResponse;
    jusoList: JusoItem[];
  };
}
