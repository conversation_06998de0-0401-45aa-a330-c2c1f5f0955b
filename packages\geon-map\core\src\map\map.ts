import { checkODFAvailability, ODF } from "../odf";
import type {
  Coordinate,
  MapInitializeOptions,
  ProjectionCode,
  ViewState,
} from "../types";
import { createODFCoordinate, fromODFCoordinate } from "../utils/coordinate";

/**
 * ODF Map 핵심 클래스
 * 지도 생성, 조작, 상태 관리를 담당
 */
export class Map {
  /**
   * 지도 초기화 옵션 생성
   */
  static createMapOptions(options: MapInitializeOptions): any {
    return {
      center: createODFCoordinate(options.center ?? [199312.9996, 551784.6924]),
      zoom: options.zoom ?? 11,
      projection: options.projection ?? "EPSG:5186",
      baroEMapURL:
        options.baroEMapURL ??
        "https://geon-gateway.geon.kr/map/api/map/baroemap",
      baroEMapAirURL:
        options.baroEMapAirURL ??
        "https://geon-gateway.geon.kr/map/api/map/ngisair",
      basemap: options.basemap ?? {
        baroEMap: ["eMapBasic", "eMapAIR", "eMapColor", "eMapWhite"],
      },
      optimization: options.optimization ?? true,
    };
  }

  /**
   * 지도 초기화
   */
  static async createMap(
    container: HTMLElement,
    options: MapInitializeOptions,
  ): Promise<any> {
    if (!container) {
      throw new Error("Container element is required");
    }

    try {
      const odf = await checkODFAvailability();

      const mapOptions = this.createMapOptions(options);
      const map = new ODF.Map(container, mapOptions);

      // 베이스맵 컨트롤 설정
      const basemapControl = new ODF.BasemapControl();
      basemapControl.setMap(map);
      map.basemapControl = basemapControl;

      return { mapInstance: map, odf };
    } catch (error) {
      console.error("Failed to create map:", error);
      throw error;
    }
  }

  /**
   * 현재 뷰 상태 조회
   */
  static getViewState(map: any): ViewState {
    if (!map) {
      throw new Error("Map instance is required");
    }

    try {
      const center = map.getCenter();
      const zoom = map.getZoom();
      const projection = map.getProjection().getCode();

      return {
        center: fromODFCoordinate(center),
        zoom,
        projection,
      };
    } catch (error) {
      console.error("Failed to get view state:", error);
      throw error;
    }
  }

  /**
   * 지도 중심점 설정
   */
  static setCenter(map: any, center: Coordinate): void {
    if (!map) {
      throw new Error("Map instance is required");
    }

    try {
      const odfCenter = createODFCoordinate(center);
      map.setCenter(odfCenter);
    } catch (error) {
      console.error("Failed to set center:", error);
      throw error;
    }
  }

  /**
   * 지도 줌 레벨 설정
   */
  static setZoom(map: any, zoom: number): void {
    if (!map) {
      throw new Error("Map instance is required");
    }

    try {
      map.setZoom(zoom);
    } catch (error) {
      console.error("Failed to set zoom:", error);
      throw error;
    }
  }

  /**
   * 좌표 변환
   */
  static transformCoordinate(
    map: any,
    coords: Coordinate,
    sourceProjection: ProjectionCode,
  ): Coordinate {
    if (!map) {
      throw new Error("Map instance is required");
    }

    const projection = map.getProjection();
    return projection.project(coords, sourceProjection);
  }

  /**
   * 베이스맵 변경
   */
  static setBasemap(map: any, basemapId: string): void {
    if (!map?.basemapControl) {
      throw new Error("Map or basemap control is not available");
    }

    try {
      map.basemapControl.switchBaseLayer(basemapId);
    } catch (error) {
      console.error("Failed to set basemap:", error);
      throw error;
    }
  }

  /**
   * 지도 정리
   */
  static destroyMap(map: any): void {
    if (map) {
      // ODF Map 정리 로직이 있다면 여기에 추가
      // map.destroy() 등
    }
  }
}
