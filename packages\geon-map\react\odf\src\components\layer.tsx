"use client";

import React, { useCallback, useEffect, useRef, useState } from "react";
import { ReactNode } from "react";

import { useLayer } from "../hooks/use-layer";
import type { LayerProps } from "../types/layer-types";

// 레이어 컨텍스트 인터페이스
export interface LayerContextValue {
  layerId: string;
}

// LayerContext 정의 (각 레이어 인스턴스별로 사용)
export const LayerContext = React.createContext<LayerContextValue | null>(null);

/**
 * Layer 컴포넌트 - 지도에 레이어를 추가합니다.
 * ODF 라이브러리를 React 스타일로 래핑합니다.
 */
export function Layer(props: LayerProps & { children?: ReactNode }) {
  const { addLayer, removeLayer } = useLayer();
  const [layerId, setLayerId] = useState<string | null>(null);
  const isInitializing = useRef(false);

  // 레이어 정리 함수 - 단순화
  const cleanupLayer = useCallback(() => {
    if (layerId) {
      try {
        removeLayer(layerId);
        console.log("레이어 제거됨:", layerId);
      } catch (error) {
        console.error("레이어 정리 중 오류:", error);
      } finally {
        setLayerId(null);
      }
    }
  }, [layerId, removeLayer]);

  // 레이어 초기화 함수 - 단순화
  const initLayer = useCallback(async () => {
    // 이미 초기화 중이거나 초기화된 경우 중복 초기화 방지
    if (isInitializing.current || layerId) {
      console.log("레이어 이미 초기화 중이거나 초기화됨, 스킵");
      return;
    }

    isInitializing.current = true;

    try {
      console.log("레이어 초기화 시작");
      // 레이어 추가 - 이제 ODF ID 문자열을 직접 반환
      const odfId = await addLayer(props);

      if (odfId) {
        setLayerId(odfId);
        console.log("레이어 초기화 완료, ID:", odfId);
      }
    } catch (error) {
      console.error("레이어 초기화 중 오류:", error);
    } finally {
      isInitializing.current = false;
    }
  }, [addLayer, props]);

  // 컴포넌트 마운트 시 레이어 초기화 (단 한 번만 실행)
  useEffect(() => {
    console.log("Layer 컴포넌트 마운트");
    initLayer();

    // 컴포넌트 언마운트 시 정리
    return () => {
      console.log("Layer 컴포넌트 언마운트");
      cleanupLayer();
    };
  }, []); // 의존성 배열에 cleanupLayer와 initLayer만 포함

  // 레이어 컨텍스트 값 - layerId가 있을 때만 유효
  const layerContextValue = React.useMemo(
    () => (layerId ? { layerId } : null),
    [layerId],
  );

  // layerId가 있을 때만 children 렌더링
  return (
    <>
      <LayerContext.Provider value={layerContextValue}>
        {layerId && props.children}
      </LayerContext.Provider>
    </>
  );
}
