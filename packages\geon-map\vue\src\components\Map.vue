<template>
  <div 
    ref="mapContainer" 
    :class="className"
    :style="style"
  >
    <div v-if="isLoading" class="absolute inset-0 flex items-center justify-center bg-gray-100">
      <div class="text-gray-600">지도를 로딩 중...</div>
    </div>
    <div v-if="error" class="absolute inset-0 flex items-center justify-center bg-red-50">
      <div class="text-red-600">지도 로딩 실패: {{ error }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useMap } from '../composables/useMap'

interface Props {
  className?: string
  style?: Record<string, any>
  mapOptions?: any
}

const props = withDefaults(defineProps<Props>(), {
  className: 'w-full h-96 relative',
  style: () => ({}),
  mapOptions: () => ({})
})

const mapContainer = ref<HTMLDivElement>()
const { map, isLoading, error, initializeMap, destroyMap } = useMap()

onMounted(() => {
  if (mapContainer.value) {
    initializeMap(mapContainer.value, props.mapOptions)
  }
})

onUnmounted(() => {
  destroyMap()
})

// Watch for mapOptions changes
watch(() => props.mapOptions, (newOptions) => {
  if (map.value && mapContainer.value) {
    // Re-initialize map with new options if needed
    destroyMap()
    initializeMap(mapContainer.value, newOptions)
  }
}, { deep: true })
</script>

<style scoped>
/* Map container styles */
.map-container {
  position: relative;
  overflow: hidden;
}
</style>
