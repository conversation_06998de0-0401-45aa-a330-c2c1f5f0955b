/// <reference lib="dom" />

declare global {
  interface Window {
    odf: any;
    odfMap: any;
  }
}

declare const odf: any;

import { BasemapInfo, LayerFactory } from "@geon-map/core";
import { useMemo } from "react";

import { useMapStore } from "../stores/map-store";

// 좌표 변환 유틸리티
const transformCoordinate = (
  map: any,
  coords: [number, number],
  sourceProjection: string,
): [number, number] => {
  const projection = map.getProjection();
  const transformed = projection.project(coords, sourceProjection);
  return transformed;
};

/**
 * 지도 상태와 뷰 메서드에 접근할 수 있는 훅
 * Zustand store 기반으로 동작하며 Provider가 필요하지 않습니다.
 *
 * 레이어 관련 기능이 필요한 경우 useLayer() 훅을 별도로 사용하세요.
 *
 * @example
 * ```tsx
 * function MyComponent() {
 *   const { map, view, isLoading } = useMap();
 *
 *   const handleBasemapChange = () => {
 *     view.setBasemap('eMapAIR');
 *   };
 *
 *   return <button onClick={handleBasemapChange}>항공지도</button>;
 * }
 * ```
 */

export function useMap() {
  // Zustand store에서 상태 가져오기
  const map = useMapStore((state) => state.map);
  const isLoading = useMapStore((state) => state.isLoading);
  const error = useMapStore((state) => state.error);
  const mapOptions = useMapStore((state) => state.mapOptions);
  const layerFactory = new LayerFactory(map, odf);

  // 뷰 관련 메서드들
  const view = useMemo(
    () => ({
      setCenter: (center: [number, number], fromProjection?: string) => {
        if (!map) return;
        if (fromProjection) {
          const transformed = transformCoordinate(map, center, fromProjection);
          map.setCenter(new odf.Coordinate(transformed[0], transformed[1]));
        } else {
          map.setCenter(new odf.Coordinate(center[0], center[1]));
        }
      },
      setZoom: (zoom: number) => {
        if (!map) return;
        map.setZoom(zoom);
      },
      setBasemap: (basemapId: string) => {
        if (!map?.basemapControl) return;
        map.basemapControl.switchBaseLayer(basemapId);
      },
      setBasemapInfo: (basemapInfo: BasemapInfo) => {
        if (!map) return 0;
        layerFactory.setBasemapInfo(basemapInfo);
      },
      getCenter: (): [number, number] => {
        if (!map) return [0, 0];
        try {
          const center = map.getCenter();
          return [center.x, center.y];
        } catch {
          return [0, 0];
        }
      },
      getZoom: (): number => {
        if (!map) return 0;
        try {
          return map.getZoom();
        } catch {
          return 0;
        }
      },
      getProjection: (): string => {
        if (!map) return mapOptions.projection ?? "EPSG:5186";
        try {
          return map.getProjection().getCode();
        } catch {
          return mapOptions.projection ?? "EPSG:5186";
        }
      },
      transformCoordinate: (
        coords: [number, number],
        sourceProjection: string,
      ): [number, number] => {
        if (!map) return coords;
        return transformCoordinate(map, coords, sourceProjection);
      },
      moveToCurrentLocation: (zoom: number = 15) => {
        if (!navigator.geolocation || !map) return;

        navigator.geolocation.getCurrentPosition(
          (position) => {
            const { latitude, longitude } = position.coords;
            map.setCenter(new odf.Coordinate(longitude, latitude));
            map.setZoom(zoom);
          },
          (error) => {
            console.error("위치 정보를 가져올 수 없습니다:", error);
          },
        );
      },
    }),
    [map, mapOptions.projection],
  );

  return {
    map,
    view,
    isLoading,
    error,
  };
}
