import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import dts from "vite-plugin-dts";
import { resolve } from "path";

export default defineConfig({
  resolve: {
    alias: {
      "@geon-map/core": resolve(__dirname, "../core/src/index.ts"),
    },
  },
  plugins: [
    vue(),
    dts({
      include: ["src/**/*"],
      exclude: ["src/**/*.test.*", "src/**/*.stories.*"],
      outDir: "dist",
      insertTypesEntry: true,
    }),
  ],
  build: {
    lib: {
      entry: resolve(__dirname, "src/index.ts"),
      name: "GeonMapVue",
      formats: ["es", "cjs"],
      fileName: (format) => `index.${format === "es" ? "mjs" : "cjs"}`,
    },
    rollupOptions: {
      external: [
        "vue",
        "pinia",
        "@geon-map/core",
      ],
      output: {
        preserveModules: false,
        exports: "named",
      },
    },
    sourcemap: true,
    target: "es2020",
  },
});
