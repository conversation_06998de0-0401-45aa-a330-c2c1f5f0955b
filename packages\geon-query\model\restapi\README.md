# 네이밍 규칙

## 📘 API 클라이언트 생성 함수명
**패턴**: `create[API타입][서비스명]Client`
## 📙 기본 클라이언트 인스턴스
**패턴**: `default[API타입][서비스명]Client`

### API 타입
- GEON-API의 경우 : `geon`

### GEON API 예시
- API 클라이언트 생성
```typescript
createGeonAddrgeoClient // 주소/지오코딩 API 
createGeonSmtClient // 시스템 관리 도구 API 
createGeonBuilderClient // 빌더 API
```
- 기본 클라이언트 인스턴스
```typescript
defaultGeonAddrgeoClient 
defaultGeonSmtClient 
defaultGeonBuilderClient
```
