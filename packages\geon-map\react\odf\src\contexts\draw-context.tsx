"use client";

import type {
  ContextMenuInfo,
  MeasureControlOptions,
  SelectedFeature,
} from "@geon-map/core";
import React, { createContext, useContext, useEffect } from "react";

import { useMapStore } from "../stores/map-store";

// DrawContext 타입 정의
export interface DrawContextValue {
  selectedFeature: SelectedFeature | null;
  contextMenuInfo: ContextMenuInfo | null;
  handleDeleteSelectedFeature: () => void;
  handleStyleChange: (styleOptions: any) => void;
  handleCloseContextMenu: () => void;
  handleUndo: () => void;
  handleRedo: () => void;
  canUndo: boolean;
  canRedo: boolean;
}

// DrawContext 생성
const DrawContext = createContext<DrawContextValue | null>(null);

// DrawContext Hook
export function useDrawContext(): DrawContextValue {
  const context = useContext(DrawContext);
  if (!context) {
    throw new Error("useDrawContext must be used within a DrawContextProvider");
  }
  return context;
}

// DrawProvider Props - 필요한 속성들을 직접 정의
export interface DrawProviderProps {
  children: React.ReactNode;
  /** 연속 측정 여부 */
  continuity?: boolean;
  /** 측정 옵션 활성화 여부 (선 그리기/원그리기 툴에서 활성화) */
  measure?: boolean;
  /** drawControl 생성 시 새 레이어 생성 여부 */
  createNewLayer?: boolean;
  /** 우클릭 편집 기능 */
  editFeatureMenu?: string[];
  /** 생성할 툴 배열 */
  tools?: any[];
  /** 툴팁 메세지 변경 */
  message?: any;
  /** 그리기 도형 스타일 */
  style?: any;
  /** 버퍼 도형 스타일 */
  bufferStyle?: any;
  /** MeasureControl 옵션 */
  measureOptions?: MeasureControlOptions;
}

/**
 * Draw Provider
 *
 * 그리기 관련 기능을 초기화하고 관리합니다.
 * MapProvider와 독립적으로 사용하거나 내부에서 사용할 수 있습니다.
 *
 * @example
 * ```tsx
 * <MapProvider mapOptions={{ projection: 'EPSG:4326' }}>
 *   <DrawProvider drawOptions={{ continuity: false, measure: true }}>
 *     <Map className="w-full h-96" />
 *   </DrawProvider>
 * </MapProvider>
 * ```
 */
export function DrawProvider({
  children,
  measureOptions,
  ...drawControlOptions
}: DrawProviderProps) {
  // DrawControlOptions가 비어있지 않은지 확인
  const hasDrawOptions = Object.keys(drawControlOptions).length > 0;

  // 지도가 초기화된 후 DrawManager 초기화 (한 번만)
  useEffect(() => {
    if (!hasDrawOptions) return;

    const unsubscribe = useMapStore.subscribe((state) => {
      const { map, drawManager } = state;
      if (map && !drawManager) {
        // DrawManager 초기화 - measureOptions 전달
        useMapStore
          .getState()
          .initializeDrawManager(drawControlOptions, measureOptions);
      }
    });

    return unsubscribe;
  }, []); // 의존성 제거하여 중복 초기화 방지

  // 스타일 변경 감지 및 실시간 스타일 업데이트
  useEffect(() => {
    if (!hasDrawOptions) return;

    const { drawManager } = useMapStore.getState();
    if (drawManager && drawControlOptions.style) {
      // 스타일 옵션만 업데이트 (전체 재초기화 방지)
      drawManager.updateStyle(drawControlOptions.style);
      console.log(
        "DrawProvider: Style updated in real-time",
        drawControlOptions.style,
      );
    }
  }, [drawControlOptions.style]); // style 변경만 감지

  // 컴포넌트 언마운트 시 정리
  useEffect(() => {
    return () => {
      const { drawManager } = useMapStore.getState();
      if (drawManager) {
        drawManager.destroy();
        useMapStore.getState().setDrawManager(null);
      }
    };
  }, []);

  return <>{children}</>;
}

// DrawContextProvider Props
export interface DrawContextProviderProps {
  children: React.ReactNode;
}

/**
 * DrawContextProvider
 *
 * 그리기 관련 상태와 핸들러를 제공하는 Context Provider
 * 자동으로 이벤트 핸들링을 포함하고 사용자가 컨텐츠를 커스터마이징할 수 있습니다.
 *
 * @example
 * ```tsx
 * <DrawContextProvider>
 *   <MyCustomContextMenu />
 *   <MyDrawingTools />
 * </DrawContextProvider>
 * ```
 */
export function DrawContextProvider({ children }: DrawContextProviderProps) {
  // Zustand store에서 상태 구독
  const selectedFeature = useMapStore((state) => state.selectedFeature);
  const contextMenuInfo = useMapStore((state) => state.contextMenuInfo);

  // Store 액션들
  const {
    deleteSelectedFeature,
    changeSelectedFeatureStyle,
    setSelectedFeature,
    setContextMenuInfo,
    undo,
    redo,
    canUndo,
    canRedo,
  } = useMapStore.getState();

  // 핸들러들
  const handleDeleteSelectedFeature = () => {
    deleteSelectedFeature();
  };

  const handleStyleChange = (styleOptions: any) => {
    changeSelectedFeatureStyle(styleOptions);
  };

  const handleCloseContextMenu = () => {
    // 상태 초기화 순서 개선: contextMenuInfo를 먼저 null로 설정하여
    // Popup 컴포넌트가 안전하게 언마운트되도록 함
    setContextMenuInfo(null);

    // 약간의 지연을 두고 selectedFeature 초기화
    setTimeout(() => {
      setSelectedFeature(null);
    }, 0);
  };

  const handleUndo = () => {
    undo();
  };

  const handleRedo = () => {
    redo();
  };

  // Context value
  const contextValue: DrawContextValue = {
    selectedFeature,
    contextMenuInfo,
    handleDeleteSelectedFeature,
    handleStyleChange,
    handleCloseContextMenu,
    handleUndo,
    handleRedo,
    canUndo: canUndo(),
    canRedo: canRedo(),
  };

  return (
    <DrawContext.Provider value={contextValue}>{children}</DrawContext.Provider>
  );
}
