// 정제된 basemap API (GET/POST 구분 및 타입 매칭)

import { fetcher } from "../utils/fetcher";
import { API_TYPE, apiUrl } from "../utils/geonAPI";
import {
  BasemapDeleteRequest,
  BasemapInsertRequest,
  BasemapListRequest,
  BasemapListResponse,
  BasemapSelectRequest,
  BasemapUpdateRequest,
  CommonDetailCodeRequest,
  CommonGroupCodeRequest,
  LayerGroupInfoDeleteRequest,
  LayerGroupInfoExcelRequest,
  LayerGroupInfoInsertRequest,
  LayerGroupInfoListRequest,
  LayerGroupInfoListV2Request,
  LayerGroupInfoSelectRequest,
  LayerGroupInfoUpdateRequest,
  LayerShareDeleteRequest,
  LayerShareInsertRequest,
  LayerShareSelectByUserIdRequest,
  LayerShareSelectRequest,
  LayerShareUpdateRequest,
} from "./type/smt-type";

const _API_TYPE: API_TYPE = "smt";

// 설정 타입
export interface SmtAPIConfig {
  baseUrl?: string;
  apiKey?: string;
  timeout?: number;
}

// 동적 API 클라이언트 생성 함수
export function createGeonSmtClient(config: SmtAPIConfig = {}) {
  const { baseUrl, apiKey } = config;

  return {
    // 공통 코드 API
    commonCode: {
      /** 공통 그룹 코드 조회 */
      getGroupCodes: (params: CommonGroupCodeRequest) =>
        fetcher.get<any>(
          apiUrl({
            endpoint: "/cmmn/code/group",
            type: _API_TYPE,
            baseUrl,
            apiKey,
          }),
          params,
        ),

      /** 공통 상세 코드 조회 */
      getDetailCodes: (params: CommonDetailCodeRequest) =>
        fetcher.get<any>(
          apiUrl({
            endpoint: "/cmmn/code/detail",
            type: _API_TYPE,
            baseUrl,
            apiKey,
          }),
          params,
        ),
    },

    // 레이어 공유 API
    layerShare: {
      /** 레이어 공유 등록 */
      insert: (params: LayerShareInsertRequest) =>
        fetcher.post<any, LayerShareInsertRequest>(
          apiUrl({
            endpoint: "/layer/share/insert",
            type: _API_TYPE,
            baseUrl,
            apiKey,
          }),
          params,
        ),

      /** 레이어 공유 수정 */
      update: (params: LayerShareUpdateRequest) =>
        fetcher.post<any, LayerShareUpdateRequest>(
          apiUrl({
            endpoint: "/layer/share/update",
            type: _API_TYPE,
            baseUrl,
            apiKey,
          }),
          params,
        ),

      /** 레이어 공유 삭제 */
      delete: (params: LayerShareDeleteRequest) =>
        fetcher.post<any, LayerShareDeleteRequest>(
          apiUrl({
            endpoint: "/layer/share/delete",
            type: _API_TYPE,
            baseUrl,
            apiKey,
          }),
          params,
        ),

      /** 레이어 공유 조회 */
      select: (params: LayerShareSelectRequest) =>
        fetcher.get<any>(
          apiUrl({
            endpoint: "/layer/share/select",
            type: _API_TYPE,
            baseUrl,
            apiKey,
          }),
          params,
        ),

      /** 사용자 ID로 공유된 레이어 조회 */
      selectByUserId: (params: LayerShareSelectByUserIdRequest) =>
        fetcher.get<any>(
          apiUrl({
            endpoint: "/layer/share/select/userId",
            type: _API_TYPE,
            baseUrl,
            apiKey,
          }),
          params,
        ),
    },

    // 레이어 그룹 정보 API
    layerGroupInfo: {
      /** 레이어 그룹 목록 조회 */
      list: (params: LayerGroupInfoListRequest) =>
        fetcher.get<any>(
          apiUrl({
            endpoint: "/lyrgrp/info/list",
            type: _API_TYPE,
            baseUrl,
            apiKey,
          }),
          params,
        ),

      /** 레이어 그룹 등록 */
      insert: (params: LayerGroupInfoInsertRequest) =>
        fetcher.post<any, LayerGroupInfoInsertRequest>(
          apiUrl({
            endpoint: "/lyrgrp/info/insert",
            type: _API_TYPE,
            baseUrl,
            apiKey,
          }),
          params,
        ),

      /** 레이어 그룹 수정 */
      update: (params: LayerGroupInfoUpdateRequest) =>
        fetcher.post<any, LayerGroupInfoUpdateRequest>(
          apiUrl({
            endpoint: "/lyrgrp/info/update",
            type: _API_TYPE,
            baseUrl,
            apiKey,
          }),
          params,
        ),

      /** 레이어 그룹 삭제 */
      delete: (params: LayerGroupInfoDeleteRequest) =>
        fetcher.post<any, LayerGroupInfoDeleteRequest>(
          apiUrl({
            endpoint: "/lyrgrp/info/delete",
            type: _API_TYPE,
            baseUrl,
            apiKey,
          }),
          params,
        ),

      /** 레이어 그룹 조회 */
      select: (params: LayerGroupInfoSelectRequest) =>
        fetcher.get<any>(
          apiUrl({
            endpoint: "/lyrgrp/info/select",
            type: _API_TYPE,
            baseUrl,
            apiKey,
          }),
          params,
        ),

      /** 레이어 그룹 목록 엑셀 다운로드 */
      excel: (params: LayerGroupInfoExcelRequest) =>
        fetcher.get<any>(
          apiUrl({
            endpoint: "/lyrgrp/info/excel",
            type: _API_TYPE,
            baseUrl,
            apiKey,
          }),
          params,
        ),

      /** 레이어 그룹 목록 조회(v2) */
      listV2: (params: LayerGroupInfoListV2Request) =>
        fetcher.get<any>(
          apiUrl({
            endpoint: "/lyrgrp/info/v2/list",
            type: _API_TYPE,
            baseUrl,
            apiKey,
          }),
          params,
        ),
    },

    // 베이스맵 API
    basemap: {
      /** 베이스맵 수정 */
      update: (params: BasemapUpdateRequest) =>
        fetcher.post<any, BasemapUpdateRequest>(
          apiUrl({
            endpoint: "/basemap/update",
            type: _API_TYPE,
            baseUrl,
            apiKey,
          }),
          params,
        ),

      /** 베이스맵 insert */
      insert: (params: BasemapInsertRequest) =>
        fetcher.post<any, BasemapInsertRequest>(
          apiUrl({
            endpoint: "/basemap/insert",
            type: _API_TYPE,
            baseUrl,
            apiKey,
          }),
          params,
        ),

      /** 베이스맵 delete */
      delete: (params: BasemapDeleteRequest) =>
        fetcher.post<any, BasemapDeleteRequest>(
          apiUrl({
            endpoint: "/basemap/delete",
            type: _API_TYPE,
            baseUrl,
            apiKey,
          }),
          params,
        ),

      /** 베이스맵 조회 */
      select: (params: BasemapSelectRequest) =>
        fetcher.get<any>(
          apiUrl({
            endpoint: "/basemap/select",
            type: _API_TYPE,
            baseUrl,
            apiKey,
          }),
          params,
        ),

      /** 베이스맵 목록 */
      list: (params: BasemapListRequest) =>
        fetcher.get<BasemapListResponse>(
          apiUrl({
            endpoint: "/basemap/list",
            type: _API_TYPE,
            baseUrl,
            apiKey,
          }),
          params,
        ),
    },
  };
}

export const defaultGeonSmtClient = createGeonSmtClient();
