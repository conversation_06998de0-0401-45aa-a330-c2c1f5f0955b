import { computed } from "vue";
import { useMapStore } from "../stores/mapStore";
import type { DrawingMode, DrawingFeature } from "@geon-map/core";

/**
 * Vue composable for drawing functionality
 *
 * @example
 * ```vue
 * <script setup>
 * import { useDraw } from '@geon-map/vue'
 *
 * const {
 *   features,
 *   drawingMode,
 *   isDrawing,
 *   startDrawing,
 *   stopDrawing,
 *   clearAllFeatures,
 *   importFromGeoJSON
 * } = useDraw()
 *
 * const handleStartDrawing = () => {
 *   startDrawing('polygon')
 * }
 * </script>
 * ```
 */
export function useDraw() {
  // Nuxt.js 환경에서 안전하게 store 사용
  let mapStore: ReturnType<typeof useMapStore>;

  try {
    mapStore = useMapStore();
  } catch (error) {
    // SSR 환경이나 Pinia가 초기화되지 않은 경우 처리
    console.warn("MapStore not available:", error);
    return {
      features: computed(() => []),
      drawingMode: computed(() => null),
      isDrawing: computed(() => false),
      drawManager: computed(() => null),
      startDrawing: () => {},
      stopDrawing: () => {},
      deleteFeature: () => {},
      clearAllFeatures: () => {},
      exportToGeoJSON: () => null,
      importFromGeoJSON: () => [],
      getDrawState: () => ({
        activeMode: null,
        features: [],
        isDrawing: false,
        isContinuous: false,
      }),
      getDrawLayer: () => null,
      getDrawControl: () => null,
    };
  }

  // Computed properties for reactive access
  const features = computed(() => mapStore.features);
  const drawingMode = computed(() => mapStore.drawingMode);
  const isDrawing = computed(() => mapStore.isDrawing);
  const drawManager = computed(() => mapStore.drawManager);

  // Drawing actions
  const startDrawing = (mode: DrawingMode) => {
    mapStore.startDrawing(mode);
  };

  const stopDrawing = () => {
    mapStore.stopDrawing();
  };

  const clearAllFeatures = () => {
    mapStore.clearFeatures();
  };

  const deleteFeature = (featureId: string) => {
    mapStore.removeFeature(featureId);
  };

  // GeoJSON operations
  const exportToGeoJSON = () => {
    if (!drawManager.value) {
      console.warn("DrawManager is not initialized");
      return null;
    }
    return drawManager.value.exportToGeoJSON();
  };

  const importFromGeoJSON = (
    geoJsonData: any,
    styleOptions?: any
  ): DrawingFeature[] => {
    if (!drawManager.value) {
      console.warn("DrawManager is not initialized");
      return [];
    }

    // DrawManager에서 처리된 features 반환받기 (stateless)
    const rawImportedFeatures = drawManager.value.importFromGeoJSON(
      geoJsonData,
      styleOptions
    );

    // readonly 타입 문제 해결을 위해 깊은 복사로 mutable 객체 생성
    const importedFeatures: DrawingFeature[] = rawImportedFeatures.map(
      (feature) => ({
        id: feature.id,
        type: feature.type,
        coordinates: feature.coordinates,
        geometry: feature.geometry,
        properties: feature.properties ? { ...feature.properties } : {},
        createdAt: feature.createdAt,
        style: feature.style
          ? {
              fill: feature.style.fill
                ? {
                    color: [...feature.style.fill.color] as [
                      number,
                      number,
                      number,
                      number,
                    ],
                  }
                : undefined,
              stroke: feature.style.stroke
                ? {
                    color: [...feature.style.stroke.color] as [
                      number,
                      number,
                      number,
                      number,
                    ],
                    width: feature.style.stroke.width,
                  }
                : undefined,
              image: feature.style.image,
              text: feature.style.text,
            }
          : undefined,
      })
    );

    if (importedFeatures.length > 0) {
      // 현재 features에 추가 (타입 캐스팅으로 readonly 문제 해결)
      const currentFeatures = mapStore.features as DrawingFeature[];
      const newFeatures = [...currentFeatures, ...importedFeatures];
      mapStore.setFeatures(newFeatures);
    }

    return importedFeatures;
  };

  // Advanced features
  const getDrawState = () => {
    return {
      activeMode: drawingMode.value,
      features: features.value,
      isDrawing: isDrawing.value,
      isContinuous: false, // 필요시 store에 추가
    };
  };

  const getDrawLayer = () => {
    if (!drawManager.value) return null;
    return drawManager.value.getDrawLayer();
  };

  const getDrawControl = () => {
    if (!drawManager.value) return null;
    return drawManager.value.getDrawControl();
  };

  return {
    // State
    features,
    drawingMode,
    isDrawing,
    drawManager,

    // Basic actions
    startDrawing,
    stopDrawing,

    // Feature management
    deleteFeature,
    clearAllFeatures,

    // GeoJSON operations
    exportToGeoJSON,
    importFromGeoJSON,

    // Advanced features
    getDrawState,
    getDrawLayer,
    getDrawControl,
  };
}
