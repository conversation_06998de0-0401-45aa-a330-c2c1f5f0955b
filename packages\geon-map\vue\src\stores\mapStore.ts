import { defineStore } from "pinia";
import { ref, computed, readonly } from "vue";
import { DrawManager, MapUtils } from "@geon-map/core";
import type { DrawingFeature, DrawingMode, MapInitializeOptions } from "@geon-map/core";

export const useMapStore = defineStore("map", () => {
  // State
  const map = ref<any>(null);
  const drawManager = ref<DrawManager | null>(null);
  const features = ref<DrawingFeature[]>([]);
  const drawingMode = ref<DrawingMode | null>(null);
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  
  // Getters
  const isDrawing = computed(() => drawingMode.value !== null);

  // Actions
  const initializeMap = async (container: HTMLElement, options: MapInitializeOptions = {}) => {
    try {
      isLoading.value = true;
      error.value = null;

      console.log("Map initialization started");

      // React 버전과 동일한 MapUtils.createMap 사용
      const mapInstance = await MapUtils.createMap(container, options);
      map.value = mapInstance;

      console.log("Map initialized successfully");
    } catch (err) {
      error.value =
        err instanceof Error ? err.message : "Failed to initialize map";
      console.error("Failed to initialize map:", err);
    } finally {
      isLoading.value = false;
    }
  };

  const initializeDrawManager = (options: any = {}) => {
    if (!map.value) {
      console.warn("Map is not initialized");
      return;
    }

    try {
      drawManager.value = new DrawManager();
      drawManager.value.initialize(map.value, options);

      // drawend 이벤트 리스너 설정
      drawManager.value.addEventListener("drawend", (feature: any) => {
        const featureId =
          feature?.getId?.() ||
          `drawing_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

        const drawingFeature: DrawingFeature = {
          id: featureId,
          type: drawingMode.value,
          coordinates: feature?.getGeometry?.()?.getCoordinates?.() || [],
          geometry: feature?.getGeometry?.() || null,
          properties: feature?.getProperties?.() || {},
          createdAt: new Date(),
        };

        features.value.push(drawingFeature);
        console.log("Drawing completed:", drawingFeature);
      });

      console.log("DrawManager initialized successfully");
    } catch (err) {
      error.value =
        err instanceof Error ? err.message : "Failed to initialize DrawManager";
      console.error("Failed to initialize DrawManager:", err);
    }
  };

  const startDrawing = (mode: DrawingMode) => {
    if (!drawManager.value) {
      console.warn("DrawManager is not initialized");
      return;
    }

    drawingMode.value = mode;
    drawManager.value.startDrawing(mode);
    console.log(`Drawing started with mode: ${mode}`);
  };

  const stopDrawing = () => {
    if (!drawManager.value) return;

    drawingMode.value = null;
    drawManager.value.stopDrawing();
    console.log("Drawing stopped");
  };

  const clearFeatures = () => {
    if (drawManager.value) {
      drawManager.value.clear();
    }
    features.value = [];
    console.log("All features cleared");
  };

  const removeFeature = (featureId: string) => {
    const index = features.value.findIndex((f) => f.id === featureId);
    if (index !== -1) {
      features.value.splice(index, 1);
      console.log("Feature removed:", featureId);
    }
  };

  const setFeatures = (newFeatures: DrawingFeature[]) => {
    features.value = newFeatures;
  };

  const destroyMap = () => {
    if (drawManager.value) {
      drawManager.value.destroy();
      drawManager.value = null;
    }

    if (map.value) {
      // ODF Map cleanup if needed
      map.value = null;
    }

    features.value = [];
    drawingMode.value = null;
    error.value = null;
    console.log("Map destroyed");
  };

  return {
    // State
    map: readonly(map),
    drawManager: readonly(drawManager),
    features: readonly(features),
    drawingMode,
    isLoading: readonly(isLoading),
    error: readonly(error),

    // Getters
    isDrawing,

    // Actions
    initializeMap,
    initializeDrawManager,
    startDrawing,
    stopDrawing,
    clearFeatures,
    removeFeature,
    setFeatures,
    destroyMap,
  };
});
