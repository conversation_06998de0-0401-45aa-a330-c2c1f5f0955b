import { SingletonBase } from "@/SingletonBase";

import type { MarkerOptions } from "../types/marker";

/**
 * 마커 팩토리 클래스
 */
export class MarkerFactory extends SingletonBase {
  constructor() {
    super("Marker");
  }
  static getInstance(): MarkerFactory {
    const instance = super._getInstance.call(
      this,
      "MarkerFactory 싱글톤",
    ) as MarkerFactory;
    return instance;
  }
  /**
   * ODF 마커 인스턴스 생성
   */
  static createODFMarker(options: MarkerOptions): any {
    if (typeof (globalThis as any).odf === "undefined") {
      throw new Error("ODF library is not loaded");
    }

    return new (globalThis as any).odf.Marker({
      position: options.position,
      draggable: options.draggable ?? false,
      positioning: options.positioning ?? "bottom-center",
      offset: options.offset ?? [0, 0],
      stopEvent: options.stopEvent ?? false,
      style: options.style,
      autoPan: options.autoPan ?? false,
      autoPanAnimation: options.autoPanAnimation ?? 250,
      autoPanMargin: options.autoPanMargin ?? 20,
    });
  }

  /**
   * 마커 이벤트 리스너 등록
   */
  static registerEventListeners(
    odfMarker: any,
    eventHandlers: any,
  ): Map<string, any> {
    if (typeof (globalThis as any).odf === "undefined") {
      return new Map();
    }

    const listeners = new Map<string, any>();
    const odf = (globalThis as any).odf;

    if (eventHandlers.onClick) {
      const listener = odf.event.addListener(odfMarker, "click", (e: any) =>
        eventHandlers.onClick?.(e, odfMarker),
      );
      listeners.set("click", listener);
    }

    if (eventHandlers.onDragStart) {
      const listener = odf.event.addListener(
        odfMarker,
        "markerdragstart",
        (e: any) => eventHandlers.onDragStart?.(e, odfMarker),
      );
      listeners.set("markerdragstart", listener);
    }

    if (eventHandlers.onDrag) {
      const listener = odf.event.addListener(
        odfMarker,
        "markerdrag",
        (e: any) => eventHandlers.onDrag?.(e, odfMarker),
      );
      listeners.set("markerdrag", listener);
    }

    if (eventHandlers.onDragEnd) {
      const listener = odf.event.addListener(
        odfMarker,
        "markerdragend",
        (e: any) => eventHandlers.onDragEnd?.(e, odfMarker),
      );
      listeners.set("markerdragend", listener);
    }

    if (eventHandlers.onPositionChange) {
      const listener = odf.event.addListener(
        odfMarker,
        "change:position",
        (e: any) => eventHandlers.onPositionChange?.(e, odfMarker),
      );
      listeners.set("change:position", listener);
    }

    if (eventHandlers.onMapChange) {
      const listener = odf.event.addListener(
        odfMarker,
        "change:map",
        (e: any) => eventHandlers.onMapChange?.(e, odfMarker),
      );
      listeners.set("change:map", listener);
    }

    return listeners;
  }

  /**
   * 마커 이벤트 리스너 제거
   */
  static removeEventListeners(listeners: Map<string, any>): void {
    if (typeof (globalThis as any).odf === "undefined") return;

    const odf = (globalThis as any).odf;

    for (const [eventType, listener] of listeners) {
      try {
        odf.event.removeListener(listener);
      } catch (error) {
        console.error(
          `Failed to remove event listener for ${eventType}:`,
          error,
        );
      }
    }
  }
}
