import { SingletonBase } from "../SingletonBase";
import { ODF, ODF_MAP } from "../types";
import { MapSystemEventType, OneTimeEventOption } from "./../types/event";

export class Event extends SingletonBase {
  // private 필드 선언
  #map: ODF_MAP;
  #odf: ODF;
  mousePositionId: any = null;
  coordinateCallback?: (coord: { x: number; y: number }) => void;
  constructor(map: ODF_MAP, odf: ODF) {
    super("layerFactory");
    this.#map = map;
    this.#odf = odf;
    console.log(this.#map);
    console.log(this.#odf);
  }
  removeListener(eventId: string) {
    this.#odf.event.removeListener(eventId);
  }
  setCoordinateCallback(callback: (coord: { x: number; y: number }) => void) {
    this.coordinateCallback = callback;
  }
  addListener(
    eventType: MapSystemEventType,
    callback: (evt: any) => void,
    expiration: OneTimeEventOption,
  ) {
    this.#odf.event.addListener(this.#map, eventType, callback, expiration);
  }
  mousePositionOn(projCoord = 4326) {
    this.mousePositionId = this.#odf.event.addListener(
      this.#map,
      "pointermove",
      (evt: any) => {
        const coord = this.#map
          .getProjection()
          .unproject(evt.coordinate, projCoord);
        //console.log(coord);
        this.coordinateCallback?.({ x: coord[0], y: coord[1] });
      },
    );
  }
  mousePositionOff() {
    if (this.mousePositionId !== null)
      this.#odf.event.removeListener(this.mousePositionId);
  }

  static getInstance(map: ODF_MAP, odf: ODF): Event {
    const instance = super._getInstance.call(this, "Event 싱글톤") as Event;

    // 최초 생성 시에만 초기화
    if (!instance.#map || !instance.#odf) {
      if (!map || !odf) {
        throw new Error("Event 싱글톤 초기화 시 map과 odf가 필요합니다.");
      }
      instance.#map = map;
      instance.#odf = odf;
    }

    return instance;
  }
}
