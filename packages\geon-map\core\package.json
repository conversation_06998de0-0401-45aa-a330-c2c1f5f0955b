{"name": "@geon-map/core", "version": "0.0.1", "type": "module", "exports": {".": {"development": {"types": "./src/index.ts", "default": "./src/index.ts"}, "types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "sideEffects": false, "files": ["dist/**", "src/**"], "scripts": {"build": "tsup", "dev:prod": "tsup --watch", "prepublishOnly": "pnpm run build", "test": "vitest", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "devDependencies": {"@config/eslint": "workspace:*", "@config/typescript": "workspace:*", "@geon-query/react-query": "workspace:*", "@types/node": "^22.17.0", "eslint": "^9.32.0", "tsup": "^8.5.0", "typescript": "^5.8.3", "vitest": "^2.1.9"}, "publishConfig": {"access": "restricted"}}