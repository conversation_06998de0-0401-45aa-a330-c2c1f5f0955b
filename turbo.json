{"$schema": "https://turborepo.com/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "env": ["ANALYZE", "NODE_ENV"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"cache": false, "persistent": true}, "dev:compile": {"cache": false, "persistent": true}}}