"use client";

import {
  <PERSON>ordinate,
  Draw,
  <PERSON>,
  <PERSON><PERSON>,
  MarkerEventHandlers,
} from "@geon-map/core";
import { useMapStore } from "@geon-map/react-odf";
import { cn } from "@geon-ui/react/lib/utils";
import { Label } from "@geon-ui/react/primitives/label";
import { Switch } from "@geon-ui/react/primitives/switch";
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@geon-ui/react/primitives/tooltip";
import { Resizable } from "re-resizable";
import { useState } from "react";
import { useEffect, useRef } from "react";

import MapMarkerIcon from "../../resource/images/map-marker.png";

declare global {
  interface Window {
    kakao: any;
    KakaoRoadView: any;
  }
}
const roadviewLayerOption = {
  service: "xyz",
  projection: "EPSG:5181",
  extent: [-30000, -60000, 494288, 988576],
  tileGrid: {
    origin: [-30000, -60000],
    resolutions: [
      4096, 2048, 1024, 512, 256, 128, 64, 32, 16, 8, 4, 2, 1, 0.5, 0.25, 0.125,
    ],
    matrixIds: [
      "0",
      "1",
      "2",
      "3",
      "4",
      "5",
      "6",
      "7",
      "8",
      "9",
      "10",
      "11",
      "12",
      "13",
      "14",
      "15",
    ],
  },
  server: {
    //url: "https://map.daumcdn.net/map_k3f_prod/bakery/image_map_png/PNGSD_RV01/v16_o9kb2/{{15-z}}/{{-y-1}}/{{x}}.png",
    url: "/api/daum/map_k3f_prod/bakery/image_map_png/PNGSD_RV01/v16_o9kb2/{{15-z}}/{{-y-1}}/{{x}}.png",
  },
};
export const RoadviewTrigger = ({
  className,
  children,
  onMarkerSelect,
  marker,
  onResize,
  onCenterSelect,
}: {
  className?: string;
  children: (enabled: boolean) => React.ReactNode;
  onMarkerSelect: (marker: any) => void; // ← 추가
  marker: any;
  onResize?: (size: { width: number; height: number }) => void;
  onCenterSelect?: (center: [number, number]) => void; // ← 추가
}) => {
  const [enabled, setEnabled] = useState(false);
  const [resizableWidth, setResizableWidth] = useState(160);
  const [prevWidth, setPrevWidth] = useState(420); // 마지막 로드뷰 width 기억
  const [roadviewLayer, setLoadviewLayer] = useState<any | null>(null);
  const DrawInstance = Draw.getInstance();

  //const MarkerInstance = Marker.getInstance();
  const odf = useMapStore((state) => state.odf);
  const map = useMapStore((state) => state.map);

  const enableRoadView = () => {
    setEnabled(true);
    setResizableWidth(prevWidth);
  };
  const roadviewLayerOn = () => {
    if (odf && map) {
      const roadviewLayer = odf.LayerFactory.produce(
        "api",
        roadviewLayerOption,
      );
      setLoadviewLayer(roadviewLayer);
      roadviewLayer.setOpacity(0.5);
      roadviewLayer.setMap(map);
    }
  };
  const roadviewLayerOff = () => {
    map?.removeLayer(roadviewLayer);
  };
  const disableRoadView = () => {
    setEnabled(false);
    setPrevWidth(resizableWidth);
    setResizableWidth(160);
    roadviewLayerOff();
  };

  const handlers: MarkerEventHandlers = {
    onDragEnd: (event) => {
      const newPosition: { _x: number; _y: number } = event.getPosition();
      console.log("마커 드래그 종료 위치:", newPosition);
      //마커 위치에 따라 지도 위치 변경
      map?.setCenter(newPosition);
      onCenterSelect?.([newPosition._x, newPosition._y]); // ← 전달
    },
  };
  const handleToggle = (checked: boolean) => {
    console.log("checked", checked);
    if (checked) {
      roadviewLayerOn();
    }

    if (odf && map && checked) {
      const eventInstance = Event.getInstance(map, odf);
      alert("지도에 지점을 선택해주세요");

      DrawInstance.pointCompleted().completed((feature, eventId) => {
        console.log("eventId", eventId);
        console.log("feature", feature);
        console.log("DrawInstance", DrawInstance);
        console.log("MapMarkerIcon", MapMarkerIcon);
        //좌표가 5179로 나옴 왜지????
        const centerPoint = feature.getCenterPoint();
        onCenterSelect?.(centerPoint); // ← 전달
        const src = MapMarkerIcon.src;
        const marker = Marker.createAndAddMarker(
          map,
          {
            position: new odf.Coordinate(centerPoint),
            draggable: true,
            style: {
              //src: "./resource/images/map-marker.png",
              src,
              height: "50px",
              width: "50px",
            },
          },
          handlers,
        ).odfMarker;
        onMarkerSelect(marker);
        enableRoadView();
        //필요 없을듯.. eventExpiration를 true 설정함
        eventInstance.removeListener(eventId);
        eventInstance.addListener(
          "postcompose",
          () => {
            DrawInstance.deleteFeature(feature);
          },
          true,
        );
      });
    } else {
      Marker.removeMarker(marker);
      onMarkerSelect(null);
      disableRoadView();
    }
  };
  return (
    <Resizable
      size={{ width: resizableWidth, height: "auto" }}
      style={{ position: "absolute" }}
      minWidth={160}
      maxWidth={1000}
      maxHeight={640}
      enable={{
        top: true,
        right: true,
        bottom: true,
        left: true,
        topRight: true,
        bottomRight: true,
        bottomLeft: true,
        topLeft: true,
      }}
      onResizeStop={(_, __, ref) => {
        const width = ref.offsetWidth;
        const height = ref.offsetHeight;
        setResizableWidth(width);
        if (enabled) {
          setPrevWidth(width); // 로드뷰 켜진 상태일 때만 기억
        }
        onResize?.({ width, height });
      }}
      className={cn(
        "absolute right-4 bottom-40 z-[100] rounded-lg border p-4 shadow-sm bg-white text-sm space-y-3",
        className,
      )}
    >
      <div className="flex items-center gap-3">
        <Label htmlFor="roadview-switch" className="text-sm font-medium">
          로드뷰 표시
        </Label>
        <Switch
          id="roadview-switch"
          checked={enabled}
          onCheckedChange={handleToggle}
        />
        {enabled && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="text-[13px] text-gray-700 cursor-help truncate max-w-[150px]">
                  로드뷰를 조작 후 방향키, 스페이스 바를 이용하여 조작이
                  가능합니다.
                </div>
              </TooltipTrigger>
              <TooltipContent
                side="top"
                sideOffset={9}
                className="text-[13px] max-w-[250px]"
              >
                로드뷰를 조작 후 방향키, 스페이스 바를 이용하여 조작이
                가능합니다.
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>

      {children(enabled)}
    </Resizable>
  );
};
const API_KEY = "40f4f6b64e7ede9838110b5197156f44";

export const RoadviewContent = ({
  width,
  height,
  center,
  marker,
}: {
  width: number;
  height: number;
  center: [number, number];
  marker: any;
}) => {
  const rvWrapperRef = useRef<HTMLDivElement>(null);
  const rvRef = useRef<any>(null);
  const rvClientRef = useRef<any>(null);
  const map = useMapStore((state) => state.map);
  const odf = useMapStore((state) => state.odf);

  useEffect(() => {
    if (map && odf && rvWrapperRef.current) {
      loadKakaoScript().then(() => {
        initRoadview();
      });
    }
  }, [map, odf]);

  const loadKakaoScript = () => {
    return new Promise<void>((resolve) => {
      if (window.kakao?.maps) {
        window.kakao.maps.load(resolve);
        return;
      }

      const script = document.createElement("script");
      script.src = `//dapi.kakao.com/v2/maps/sdk.js?appkey=${API_KEY}&autoload=false`;
      script.onload = () => window.kakao.maps.load(resolve);
      document.head.appendChild(script);
    });
  };

  const to_5179 = (x: number, y: number) => {
    if (odf) {
      const mapProjection = [x, y];
      const projection4326 = new odf.Projection({ EPSG: "4326" });
      const projection5179 = projection4326.unproject(mapProjection, "5179");
      return projection5179;
    }
  };
  const to_4326 = () => {
    if (odf) {
      // const projection5186 = new odf.Projection({ EPSG: "5186" });
      const projection = map?.getProjection();
      const center4326 = projection.unproject(center, "4326");
      return new window.kakao.maps.LatLng(center4326[1], center4326[0]);
    }
  };

  const moveRoadview = () => {
    const position = to_4326();
    if (!position) return;

    rvClientRef?.current?.getNearestPanoId(position, 50, (panoId: number) => {
      if (!panoId) {
        // alert("도로가 존재하지 않는 지역입니다.");
        console.warn("도로가 존재하지 않는 지역입니다.");
      } else {
        rvRef.current.setPanoId(panoId, position);
        rvRef.current.relayout();
      }
    });
  };

  const roadviewEvent = () => {
    //로드뷰에 좌표가 바뀌었을 때 발생하는 이벤트를 등록합니다
    window.kakao.maps.event.addListener(
      rvRef.current,
      "position_changed",
      () => {
        if (odf && marker) {
          // 현재 로드뷰의 위치 좌표를 얻어옵니다
          const rvPosition = rvRef.current.getPosition();
          // 부모 배경지도 위 odf 마커 이동.
          const xy5179 = to_5179(rvPosition["La"], rvPosition["Ma"]);
          console.log(xy5179);
          marker.setPosition(new odf.Coordinate(xy5179));
          //로드뷰 움직임에 따라 지도 이동시켜주기
          map?.setCenter(new odf.Coordinate(xy5179));
        }
      },
    );
  };
  const initRoadview = () => {
    if (!rvWrapperRef.current) return;
    rvRef.current = new window.kakao.maps.Roadview(rvWrapperRef.current);
    rvClientRef.current = new window.kakao.maps.RoadviewClient();
    roadviewEvent();
    moveRoadview();
  };

  useEffect(() => {
    if (odf && map) {
      loadKakaoScript().then(() => {
        moveRoadview();
      });
    }
  }, [center]);

  return (
    <div
      ref={rvWrapperRef}
      style={{ width: width - 20, height: height - 60 }}
      className="mt-2 border rounded w-[400px] h-[300px] overflow-hidden"
    />
  );
};
export const RoadviewWidget = () => {
  const [size, setSize] = useState({ width: 420, height: 300 });
  const [center, setCenter] = useState<Coordinate>([0, 0]); // ← 추가
  const [marker, setMaker] = useState<any>(null);
  return (
    <RoadviewTrigger
      onResize={setSize}
      onCenterSelect={setCenter}
      onMarkerSelect={setMaker}
      marker={marker}
    >
      {(enabled) =>
        enabled &&
        center[0] > 0 && (
          <RoadviewContent
            width={size.width}
            height={size.height}
            center={center}
            marker={marker}
          />
        )
      }
    </RoadviewTrigger>
  );
};
