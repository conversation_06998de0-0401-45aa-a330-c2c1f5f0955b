"use client";

import { useMapStore } from "@geon-map/react-odf";
import { cn } from "@geon-ui/react/lib/utils";
import { Label } from "@geon-ui/react/primitives/label";
import { Switch } from "@geon-ui/react/primitives/switch";
import { Resizable } from "re-resizable";
import { useState } from "react";
import { useEffect, useRef } from "react";

declare global {
  interface Window {
    kakao: any;
    KakaoRoadView: any;
  }
}
export const RoadviewTrigger = ({
  className,
  children,
  onResize,
}: {
  className?: string;
  children: (enabled: boolean) => React.ReactNode;
  onResize?: (size: { width: number; height: number }) => void;
}) => {
  const [enabled, setEnabled] = useState(false);
  const [resizableWidth, setResizableWidth] = useState(160);
  const [prevWidth, setPrevWidth] = useState(420); // 마지막 로드뷰 width 기억

  const handleToggle = (checked: boolean) => {
    setEnabled(checked);

    if (checked) {
      // 로드뷰 켜짐: 마지막 사이즈로 복원
      setResizableWidth(prevWidth);
    } else {
      // 로드뷰 꺼짐: 현재 width 저장, 최소 사이즈로 축소
      setPrevWidth(resizableWidth);
      setResizableWidth(160);
    }
  };

  return (
    <Resizable
      size={{ width: resizableWidth, height: "auto" }}
      style={{ position: "absolute" }}
      minWidth={160}
      maxWidth={1000}
      maxHeight={640}
      enable={{
        top: true,
        right: true,
        bottom: true,
        left: true,
        topRight: true,
        bottomRight: true,
        bottomLeft: true,
        topLeft: true,
      }}
      onResizeStop={(_, __, ref) => {
        const width = ref.offsetWidth;
        const height = ref.offsetHeight;
        setResizableWidth(width);
        if (enabled) {
          setPrevWidth(width); // 로드뷰 켜진 상태일 때만 기억
        }
        onResize?.({ width, height });
      }}
      className={cn(
        "absolute right-4 bottom-40 z-[100] rounded-lg border p-4 shadow-sm bg-white text-sm space-y-3",
        className,
      )}
    >
      <div className="flex items-center gap-3">
        <Label htmlFor="roadview-switch" className="text-sm font-medium">
          로드뷰 표시
        </Label>
        <Switch
          id="roadview-switch"
          checked={enabled}
          onCheckedChange={handleToggle}
        />
      </div>

      {children(enabled)}
    </Resizable>
  );
};
const API_KEY = "40f4f6b64e7ede9838110b5197156f44";

export const RoadviewContent = ({
  width,
  height,
}: {
  width: number;
  height: number;
}) => {
  const rvWrapperRef = useRef<HTMLDivElement>(null);
  const rvRef = useRef<any>(null);
  const rvClientRef = useRef<any>(null);
  const kakaoMapProjectionRef = useRef<any>(null);

  const map = useMapStore((state) => state.map);
  const odf = useMapStore((state) => state.odf);

  useEffect(() => {
    if (map && odf && rvWrapperRef.current) {
      loadKakaoScript().then(() => {
        initRoadview();
        window.KakaoRoadView = {
          moveRoadview,
          mapProjection: [],
        };
      });

      window.addEventListener("beforeunload", () => {
        window.opener?.KakaoRoadView?.closeRoadView?.();
      });
    }
  }, [map, odf]);

  const loadKakaoScript = () => {
    return new Promise<void>((resolve) => {
      if (window.kakao?.maps) {
        window.kakao.maps.load(resolve);
        return;
      }

      const script = document.createElement("script");
      script.src = `//dapi.kakao.com/v2/maps/sdk.js?appkey=${API_KEY}&autoload=false`;
      script.onload = () => window.kakao.maps.load(resolve);
      document.head.appendChild(script);
    });
  };

  const to4326From5186 = () => {
    kakaoMapProjectionRef.current = new window.kakao.maps.LatLng(
      36.49767481772415,
      127.30213456755791,
    );
    return kakaoMapProjectionRef.current;
  };

  const moveRoadview = () => {
    const position = to4326From5186();
    if (!position) return;

    rvClientRef.current.getNearestPanoId(position, 50, (panoId: number) => {
      if (!panoId) {
        alert("도로가 존재하지 않는 지역입니다.");
      } else {
        rvRef.current.setPanoId(panoId, position);
        rvRef.current.relayout();
      }
    });
  };

  const initRoadview = () => {
    if (!rvWrapperRef.current) return;
    rvRef.current = new window.kakao.maps.Roadview(rvWrapperRef.current);
    rvClientRef.current = new window.kakao.maps.RoadviewClient();

    moveRoadview();

    window.kakao.maps.event.addListener(
      rvRef.current,
      "position_changed",
      () => {
        const rvPosition = rvRef.current.getPosition();
        kakaoMapProjectionRef.current = rvPosition;
      },
    );
  };

  return (
    <div
      ref={rvWrapperRef}
      style={{ width: width - 20, height: height - 60 }}
      className="mt-2 border rounded w-[400px] h-[300px] overflow-hidden"
    />
  );
};
export const RoadviewWidget = () => {
  const [size, setSize] = useState({ width: 420, height: 300 });

  return (
    <RoadviewTrigger onResize={setSize}>
      {(enabled) =>
        enabled && <RoadviewContent width={size.width} height={size.height} />
      }
    </RoadviewTrigger>
  );
};
