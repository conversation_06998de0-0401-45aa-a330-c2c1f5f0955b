"use client";
import { isOptionEnabled } from "@geon-map/core";
import { But<PERSON> } from "@geon-ui/react/primitives/button";
import { Input } from "@geon-ui/react/primitives/input";
import { Label } from "@geon-ui/react/primitives/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@geon-ui/react/primitives/select";
import { cn } from "@geon-ui/react/utils";
import { Search, SearchX, X } from "lucide-react";
import * as React from "react";
import { useCallback, useMemo, useState } from "react";

import { ADDRESS_SEARCH_OPTIONS, SEARCH_TYPE_FIELDS } from "../../constants";
import {
  AddressSearchContext,
  AddressSearchContextValue,
  isValidSearchResult,
  useAddressSearchContext,
} from "../../contexts";
import { useAddressSearch } from "../../hooks";
import {
  AddressBase,
  AddressSearchContentProps,
  AddressSearchEmptyProps,
  AddressSearchField,
  AddressSearchInputProps,
  AddressSearchItemProps,
  AddressSearchListProps,
  AddressSearchProps,
  AddressSearchRootProps,
  AddressSearchSelectProps,
  AddressSearchTriggerProps,
  AddressSearchTypes,
  RenderAddressInfoLabelClassNames,
  SearchCallback,
  SearchTypesOptions,
  VisibleFieldsOptions,
} from "../../types";

// 유틸리티 함수들
const updateFieldValue = (
  fields: AddressSearchField[],
  key: string,
  value: string,
): AddressSearchField[] =>
  fields.map((field) => (field.key === key ? { ...field, value } : field));

const getKeywordFromFields = (
  fields: AddressSearchField[],
  searchType: AddressSearchTypes,
): string => {
  // 경위도 좌표 검색의 경우 - 괄호로 명확히 구분
  if (searchType === "coordinates") {
    const lat =
      fields.find((field) => field.key === "lat")?.value?.trim() || "";
    const lng =
      fields.find((field) => field.key === "lng")?.value?.trim() || "";

    if (!lat || !lng) {
      console.warn(
        `경위도 좌표를 모두 입력해주세요 - 위도: ${lat}, 경도: ${lng}`,
      );
      return "";
    }

    // 괄호로 좌표임을 명확히 표시
    return `(${lat}, ${lng})`;
  }
  const keyword =
    fields.find((field) => field.key === searchType)?.value?.trim() || "";

  if (!keyword) {
    console.warn(`검색어 없음 - 타입: ${searchType}`);
  }

  return keyword;
};

// 검색 결과 상태 관리 헬퍼 함수들
const applySearchResult = (
  result: any,
  setters: {
    setSearchResults: (results: AddressBase[]) => void;
    setIsOpen: (open: boolean) => void;
    setHasNextPage: (hasNext: boolean) => void;
  },
) => {
  if (isValidSearchResult(result)) {
    const { resultList, hasNextPage } = result;
    setters.setSearchResults(resultList);
    setters.setIsOpen(true);
    setters.setHasNextPage(hasNextPage);
  } else {
    setters.setSearchResults([]);
    setters.setIsOpen(false);
    setters.setHasNextPage(false);
  }
};

// 검색 실행 헬퍼 함수
const executeSearch = async (
  fields: AddressSearchField[],
  searchType: AddressSearchTypes,
  onSearch: SearchCallback | undefined,
  context: {
    setIsFetching: (loading: boolean) => void;
    setCurrentPage: (page: number) => void;
    setCurrentKeyword: (keyword: string) => void;
    setSearchResults: (results: AddressBase[]) => void;
    setIsOpen: (open: boolean) => void;
    setHasNextPage: (hasNext: boolean) => void;
  },
) => {
  const sanitizedFields = fields.map(({ key, value }) => ({ key, value }));
  const keyword = getKeywordFromFields(sanitizedFields, searchType);

  if (!keyword) return;

  // 검색 상태 초기화
  context.setIsFetching(true);
  context.setCurrentPage(1);
  context.setCurrentKeyword(keyword);

  try {
    const result = await onSearch?.(sanitizedFields, searchType, 1);

    // 헬퍼 함수 사용
    applySearchResult(result, {
      setSearchResults: context.setSearchResults,
      setIsOpen: context.setIsOpen,
      setHasNextPage: context.setHasNextPage,
    });
  } catch (error) {
    console.error("Search failed:", error);
    applySearchResult(null, {
      setSearchResults: context.setSearchResults,
      setIsOpen: context.setIsOpen,
      setHasNextPage: context.setHasNextPage,
    });
  } finally {
    context.setIsFetching(false);
  }
};

const resetSearchState = (setters: {
  setSearchResults: (results: AddressBase[]) => void;
  setIsOpen: (open: boolean) => void;
  setHasNextPage: (hasNext: boolean) => void;
  setCurrentPage: (page: number) => void;
  setCurrentKeyword: (keyword: string) => void;
  setFields: (fields: AddressSearchField[]) => void;
  searchType: AddressSearchTypes; // 추가
  clearAddressFeatures: () => void; // 추가
}) => {
  setters.setSearchResults([]);
  setters.setIsOpen(false);
  setters.setHasNextPage(false);
  setters.setCurrentPage(1);
  setters.setCurrentKeyword("");
  // 입력 필드도 초기화
  const resetFields = SEARCH_TYPE_FIELDS[setters.searchType].map((field) => ({
    ...field,
    value: "", // 빈 값으로 초기화
  }));
  setters.setFields(resetFields);

  // 지도 피처 클리어
  setters.clearAddressFeatures();
};

// 안전한 key 생성 함수
const generateItemKey = (item: AddressBase, index: number): string => {
  const x = item.x ?? 0;
  const y = item.y ?? 0;
  return `coord-${x}-${y}-${index}`;
};

// 주소 정보 렌더링 컴포넌트 (React.memo 제거)
const AddressInfoRenderer = ({
  item,
  visibleFields = {
    roadAddress: true,
    poiName: true,
    jibunAddress: true,
    buildingName: true,
  },
  labelClassNames,
}: {
  item: AddressBase;
  visibleFields?: VisibleFieldsOptions;
  labelClassNames?: RenderAddressInfoLabelClassNames;
}) => {
  const { jibunAddress, roadAddress, poiName, buildingName } = item;

  const addressFields = useMemo(
    () => [
      {
        key: "roadAddress",
        value: roadAddress,
        label: "도로명",
        bgColor: "bg-[#283ebf]",
        className: labelClassNames?.roadAddress,
      },
      {
        key: "jibunAddress",
        value: jibunAddress,
        label: "지번",
        bgColor: "bg-[#00b8a3]",
        className: labelClassNames?.jibunAddress,
      },
      {
        key: "buildingName",
        value: buildingName,
        label: "건물명",
        bgColor: "bg-[#ff7200]",
        className: labelClassNames?.buildingName,
      },
      {
        key: "poiName",
        value: poiName,
        label: "poi명",
        bgColor: "bg-[#ff00bc]",
        className: labelClassNames?.poiName,
      },
    ],
    [roadAddress, jibunAddress, buildingName, poiName, labelClassNames],
  );

  return (
    <>
      {addressFields.map(
        ({ key, value, label, bgColor, className }) =>
          isOptionEnabled(visibleFields, key) &&
          value && (
            <div key={key} className="flex mb-1">
              <Label
                className={cn(
                  `${bgColor} text-white h-6 px-3 py-1 rounded-[5px] w-[80px] items-center flex-shrink-0 justify-center mr-3`,
                  className,
                )}
              >
                {label}
              </Label>
              <div className="font-medium text-sm flex-1">{value}</div>
            </div>
          ),
      )}
    </>
  );
};
AddressInfoRenderer.displayName = "AddressInfoRenderer";

// 컴포넌트들
export const AddressSearchSelect = React.forwardRef<
  HTMLButtonElement,
  AddressSearchSelectProps
>(({ className, style }, ref) => {
  const { searchType, onTypeChange, searchTypes, customOptions } =
    useAddressSearchContext();

  const selectItems = useMemo(() => {
    const allOptions: Record<string, string> = {
      ...ADDRESS_SEARCH_OPTIONS,
      ...customOptions,
    };

    return searchTypes
      ? Object.fromEntries(
          Object.entries(allOptions).filter(([key]) =>
            isOptionEnabled(searchTypes, key),
          ),
        )
      : allOptions;
  }, [searchTypes, customOptions]);

  const currentValue = useMemo(() => {
    const defaultItemKey = Object.keys(selectItems)[0] || "integrated";
    return selectItems[searchType] ? searchType : defaultItemKey;
  }, [selectItems, searchType]);

  return (
    <Select value={currentValue} onValueChange={onTypeChange}>
      <SelectTrigger
        ref={ref}
        className={cn("bg-white", className)}
        style={style}
      >
        <SelectValue placeholder="검색 타입 선택">
          {selectItems[currentValue]}
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        {Object.entries(selectItems).map(([key, label]) => (
          <SelectItem key={key} value={key}>
            {label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
});
AddressSearchSelect.displayName = "AddressSearchSelect";

export const AddressSearchInput = React.memo<AddressSearchInputProps>(
  ({ className, placeholder, ...props }) => {
    const { fields, onFieldsChange, searchType, onSearch } =
      useAddressSearchContext();
    const context = useAddressSearchContext();

    const handleInputChange = useCallback(
      (key: string, value: string) => {
        const updatedFields = updateFieldValue(fields, key, value);
        onFieldsChange(updatedFields);
      },
      [fields, onFieldsChange],
    );

    const handleKeyDown = useCallback(
      async (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === "Enter") {
          e.preventDefault();
          await executeSearch(fields, searchType, onSearch, context);
        }
      },
      [fields, searchType, onSearch, context],
    );

    if (!fields || fields.length === 0) return null;

    return (
      <>
        {fields.map((field) => (
          <Input
            key={field.key}
            className={cn(
              "bg-white",
              fields.length === 1 ? "w-full" : "flex-1",
              className,
            )}
            {...props}
            placeholder={field.placeholder || placeholder}
            value={field.value}
            onKeyDown={handleKeyDown}
            onChange={(e) => handleInputChange(field.key, e.target.value)}
          />
        ))}
      </>
    );
  },
);
AddressSearchInput.displayName = "AddressSearchInput";

export const AddressSearchTrigger = React.forwardRef<
  HTMLButtonElement,
  AddressSearchTriggerProps
>(({ className, children, ...props }, ref) => {
  const { fields, searchType, onSearch } = useAddressSearchContext();
  const context = useAddressSearchContext();

  const handleSearch = useCallback(async () => {
    await executeSearch(fields, searchType, onSearch, context);
  }, [fields, searchType, onSearch, context]);

  return (
    <Button
      ref={ref}
      className={cn(
        "whitespace-nowrap cursor-pointer hover:bg-gray-400",
        className,
      )}
      onClick={handleSearch}
      {...props}
    >
      {children ? (
        children
      ) : (
        <Search className="w-4 h-4 text-white transition-colors" />
      )}
    </Button>
  );
});
AddressSearchTrigger.displayName = "AddressSearchTrigger";

export const AddressSearchContent = React.forwardRef<
  HTMLDivElement,
  AddressSearchContentProps
>(({ className, children, ...props }, ref) => {
  const { isOpen, currentKeyword } = useAddressSearchContext();

  // 단순히 열림 상태와 검색 실행 여부만 확인
  if (!isOpen || !currentKeyword) return null;

  return (
    <div
      ref={ref}
      className={cn(
        "absolute top-full left-0 right-0 z-50 p-1 bg-white border border-gray-200 rounded-md shadow-lg",
        className,
      )}
      {...props}
    >
      {children}
    </div>
  );
});
AddressSearchContent.displayName = "AddressSearchContent";

export const AddressSearchList = React.forwardRef<
  HTMLUListElement,
  AddressSearchListProps
>(({ className, children, onLoadMore, isLoading, ...props }, ref) => {
  const {
    pagingType,
    currentPage,
    fields,
    searchType,
    setCurrentPage,
    setSearchResults,
    setIsOpen,
    searchResults,
    hasNextPage,
    setHasNextPage,
    isFetching,
    setIsFetching,
    currentKeyword,
    setCurrentKeyword,
    clearAddressFeatures,
    onFieldsChange,
  } = useAddressSearchContext();

  // children을 React.Children으로 분석
  const childrenArray = React.Children.toArray(children);
  const emptyComponent = childrenArray.find(
    (child) => React.isValidElement(child) && child.type === AddressSearchEmpty,
  );
  const otherChildren = childrenArray.filter(
    (child) =>
      !(React.isValidElement(child) && child.type === AddressSearchEmpty),
  );

  const isSearching = isLoading || isFetching;
  const hasResults = searchResults.length > 0;
  const isEmpty = !hasResults && !isSearching && currentKeyword.length > 0;
  const headerText = `"${currentKeyword}" 검색결과`;

  const handleScroll = useCallback(
    async (e: React.UIEvent<HTMLUListElement>) => {
      if (pagingType === "countable") return;

      const target = e.currentTarget;
      const isAtBottom =
        target.scrollHeight - target.scrollTop <= target.clientHeight + 10;

      if (isAtBottom && hasNextPage && !isLoading && !isFetching) {
        setIsFetching(true);
        const nextPage = currentPage + 1;
        setCurrentPage(nextPage);

        try {
          const result = await onLoadMore?.(fields, searchType, nextPage);

          if (isValidSearchResult(result)) {
            const { resultList, hasNextPage } = result;
            setSearchResults([...searchResults, ...resultList]);
            setIsOpen(true);
            setHasNextPage(hasNextPage);
          } else {
            // 헬퍼 함수 사용
            applySearchResult(null, {
              setSearchResults,
              setIsOpen,
              setHasNextPage,
            });
          }
        } catch (error) {
          console.error("Load more failed:", error);
        } finally {
          setIsFetching(false);
        }
      }
    },
    [
      pagingType,
      hasNextPage,
      isLoading,
      isFetching,
      currentPage,
      fields,
      searchType,
      onLoadMore,
      searchResults,
      setCurrentPage,
      setIsFetching,
      setSearchResults,
      setIsOpen,
      setHasNextPage,
    ],
  );

  const handleClose = useCallback(() => {
    //초기화 헬퍼 함수 사용
    resetSearchState({
      setSearchResults,
      setIsOpen,
      setHasNextPage,
      setCurrentPage,
      setCurrentKeyword,
      setFields: onFieldsChange,
      searchType: searchType,
      clearAddressFeatures,
    });
  }, [
    setSearchResults,
    setIsOpen,
    setHasNextPage,
    setCurrentPage,
    setCurrentKeyword,
    onFieldsChange,
    searchType,
    clearAddressFeatures,
  ]);

  return (
    <>
      <div className="px-3 py-2 text-sm relative font-medium text-gray-700 bg-gray-50 border-b border-gray-200 rounded-md">
        {headerText}
        <button
          onClick={handleClose}
          className="absolute top-2 right-2 p-1 hover:bg-gray-100 rounded cursor-pointer"
        >
          <X className="h-4 w-4" />
        </button>
      </div>
      <ul
        ref={ref}
        className={cn("max-h-[400px] overflow-y-auto pl-3 pr-3", className)}
        onScroll={handleScroll}
        {...props}
      >
        {/* 검색 결과가 있을 때 - AddressSearchItem 렌더링 */}
        {hasResults && otherChildren}

        {/* 검색 결과가 없을 때 - AddressSearchEmpty 렌더링 */}
        {isEmpty && emptyComponent}
      </ul>
    </>
  );
});
AddressSearchList.displayName = "AddressSearchList";

export const AddressSearchItem = React.forwardRef<
  HTMLLIElement,
  AddressSearchItemProps
>(({ className, item, labelClassNames, ...props }, ref) => {
  const { searchResults, onItemClick, visibleFields, addAddressFeature } =
    useAddressSearchContext();

  const handleClick = useCallback(
    (addressItem: AddressBase) => {
      addAddressFeature(addressItem);
      onItemClick?.(addressItem);
    },
    [addAddressFeature, onItemClick],
  );

  if (!item && searchResults.length === 0) return null;

  // 단일 아이템 렌더링
  if (item) {
    return (
      <li
        ref={ref}
        className={cn(
          "w-full cursor-pointer hover:bg-gray-50 border-b border-b-gray-300 pt-2 pb-2",
          className,
        )}
        onClick={() => handleClick(item)}
        {...props}
      >
        <AddressInfoRenderer
          item={item}
          visibleFields={visibleFields}
          labelClassNames={labelClassNames}
        />
      </li>
    );
  }

  // 검색 결과 전체 렌더링 (개선된 key 생성 함수 사용)
  return (
    <>
      {searchResults.map((resultItem, index) => (
        <li
          key={generateItemKey(resultItem, index)}
          ref={index === 0 ? ref : undefined}
          className={cn(
            "w-full cursor-pointer hover:bg-gray-50 border-b border-b-gray-300  pt-2 pb-2",
            className,
          )}
          onClick={() => handleClick(resultItem)}
          {...props}
        >
          <AddressInfoRenderer
            item={resultItem}
            visibleFields={visibleFields}
            labelClassNames={labelClassNames}
          />
        </li>
      ))}
    </>
  );
});
AddressSearchItem.displayName = "AddressSearchItem";

export const AddressSearchEmpty = React.forwardRef<
  HTMLDivElement,
  AddressSearchEmptyProps
>(({ className, children, ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn(
        "flex flex-col items-center justify-center p-8 text-center text-gray-500",
        className,
      )}
      {...props}
    >
      {children || (
        <>
          <div className="mb-3 text-2xl opacity-50">
            <SearchX />
          </div>
          <p className="text-sm font-medium">검색 결과가 없습니다.</p>
        </>
      )}
    </div>
  );
});
AddressSearchEmpty.displayName = "AddressSearchEmpty";

export const AddressSearch = React.forwardRef<
  HTMLDivElement,
  AddressSearchRootProps
>(
  (
    {
      className,
      children,
      onSearch,
      onItemClick,
      searchTypes,
      visibleFields,
      customOptions,
      defaultOpen = false,
      pagingType = "unbounded",
      onLoadMore,
      isLoading = false,
      ...props
    },
    ref,
  ) => {
    const { addAddressFeature, clearAddressFeatures } = useAddressSearch();

    const initialSearchType = useMemo<AddressSearchTypes>(() => {
      if (!searchTypes) return "integrated";
      const allOptions = { ...ADDRESS_SEARCH_OPTIONS, ...customOptions };
      const availableTypes = Object.keys(allOptions).filter((key) =>
        isOptionEnabled(searchTypes, key),
      );
      return (availableTypes[0] as AddressSearchTypes) || "integrated";
    }, [searchTypes, customOptions]);

    const [searchType, setSearchType] =
      useState<AddressSearchTypes>(initialSearchType);

    // useMemo로 최적화
    const initialFields = useMemo(
      () => SEARCH_TYPE_FIELDS[initialSearchType],
      [initialSearchType],
    );

    const [fields, setFields] = useState<AddressSearchField[]>(initialFields);
    const [searchResults, setSearchResults] = useState<AddressBase[]>([]);
    const [isOpen, setIsOpen] = useState(defaultOpen);
    const [currentPage, setCurrentPage] = useState(1);
    const [currentKeyword, setCurrentKeyword] = useState("");
    const [hasNextPage, setHasNextPage] = useState(false);
    const [isFetching, setIsFetching] = useState(false);

    const handleTypeChange = useCallback(
      (type: AddressSearchTypes) => {
        setSearchType(type);

        // 헬퍼 함수 사용
        resetSearchState({
          setSearchResults,
          setIsOpen,
          setHasNextPage,
          setCurrentPage,
          setCurrentKeyword,
          setFields,
          searchType: type,
          clearAddressFeatures,
        });
      },
      [clearAddressFeatures],
    );

    const contextValue: AddressSearchContextValue = useMemo(
      () => ({
        // 상태
        searchType,
        fields,
        searchResults,
        isOpen,
        currentPage,
        currentKeyword,
        hasNextPage,
        isLoading,
        isFetching,

        // 설정
        searchTypes,
        visibleFields,
        customOptions,
        pagingType,

        // 액션
        onTypeChange: handleTypeChange,
        onFieldsChange: setFields,
        setIsOpen,
        setSearchResults,
        setCurrentPage,
        setCurrentKeyword,
        setHasNextPage,
        setIsFetching,
        addAddressFeature,
        clearAddressFeatures,

        // 콜백
        onSearch,
        onItemClick,
        onLoadMore,
      }),
      [
        searchType,
        fields,
        searchResults,
        isOpen,
        currentPage,
        currentKeyword,
        hasNextPage,
        isLoading,
        isFetching,
        searchTypes,
        visibleFields,
        customOptions,
        pagingType,
        handleTypeChange,
        addAddressFeature,
        clearAddressFeatures,
        onSearch,
        onItemClick,
        onLoadMore,
      ],
    );

    return (
      <AddressSearchContext.Provider value={contextValue}>
        <div
          ref={ref}
          className={cn("relative space-y-4", className)}
          {...props}
        >
          {children}
        </div>
      </AddressSearchContext.Provider>
    );
  },
);
AddressSearch.displayName = "AddressSearch";

// 완성형 위젯 컴포넌트 (React.memo 제거)
export const AddressSearchWidget = ({
  onSearch,
  searchTypes,
  className,
  style,
  visibleFields,
  pagingType = "unbounded",
  onLoadMore,
  isLoading,
}: AddressSearchProps) => {
  const defaultConfig: SearchTypesOptions = {
    integrated: true,
    jibun: true,
    road: true,
    roadApi: true,
    building: true,
    coordinates: true,
    postalCode: true,
    pnu: true,
    poi: true,
  };

  return (
    <div className={cn("w-96", className)} style={style}>
      <AddressSearch
        searchTypes={searchTypes ?? defaultConfig}
        onSearch={onSearch}
        visibleFields={visibleFields}
        pagingType={pagingType}
        onLoadMore={onLoadMore}
        isLoading={isLoading}
      >
        <div className="flex gap-2">
          <AddressSearchSelect />
          <AddressSearchInput placeholder="검색어를 입력해주세요." />
          <AddressSearchTrigger></AddressSearchTrigger>
        </div>
        <AddressSearchContent>
          <AddressSearchList onLoadMore={onLoadMore} isLoading={isLoading}>
            <AddressSearchEmpty>
              {/*<div className="mb-3 text-3xl">🔍</div>*/}
              {/*<p className="text-sm font-medium mb-2">검색 결과가 없습니다</p>*/}
            </AddressSearchEmpty>
            <AddressSearchItem />
          </AddressSearchList>
        </AddressSearchContent>
      </AddressSearch>
    </div>
  );
};
AddressSearchWidget.displayName = "AddressSearchWidget";
