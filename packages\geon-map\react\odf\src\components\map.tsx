import React, { useEffect, useRef } from "react";

import { useMapSetup } from "../hooks/use-map-setup";
import { cn } from "../lib/utils";
import { useMapStore } from "../stores/map-store";
import type { MapProps } from "../types/map-types";

/**
 * Map 컴포넌트
 *
 * MapProvider 내에서 사용해야 합니다. Zustand store를 통해 상태를 관리합니다.
 *
 * @example
 * ```tsx
 * <MapProvider mapOptions={{ projection: 'EPSG:4326' }}>
 *   <Map className="w-full h-96">
 *     <Layer ... />
 *   </Map>
 * </MapProvider>
 * ```
 */
export const Map = ({ className, style, children, onMapInit }: MapProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const id = React.useId();

  // Zustand store에서 상태 가져오기
  const mapOptions = useMapStore((state) => state.mapOptions);
  const setMap = useMapStore((state) => state.setMap);
  const setOdf = useMapStore((state) => state.setOdf);
  const setLoading = useMapStore((state) => state.setLoading);
  // const setError = useMapStore((state) => state.setError);
  // const isLoading = useMapStore((state) => state.isLoading);

  // 기존 use-map hook을 사용하여 지도 초기화
  const mapState = useMapSetup({
    containerRef: containerRef as React.RefObject<HTMLDivElement>,
    autoInit: true,
    ...mapOptions, // Zustand store에서 가져온 설정
  });

  // 지도 인스턴스를 Zustand store에 설정 (한 번만)
  useEffect(() => {
    if (mapState.map) {
      setMap(mapState.map);
    }
    if (mapState.odf) {
      setOdf(mapState.odf);
    }
  }, [mapState.map]); // setMap 의존성 제거 (Zustand 액션은 안정적)

  // 로딩 상태를 Zustand store에 동기화
  useEffect(() => {
    setLoading(mapState.isLoading);
  }, [mapState.isLoading]); // setLoading 의존성 제거

  // 지도 초기화 완료 콜백 (한 번만)
  useEffect(() => {
    if (mapState.map && onMapInit) {
      console.log("지도 초기화 완료, 콜백 호출");
      onMapInit({
        odf: mapState.odf,
        map: mapState.map,
        view: mapState.view,
        isLoading: mapState.isLoading,
        error: mapState.error,
        layer: mapState.layer,
        control: mapState.control,
      });
    }
  }, [mapState.map]); // onMapInit 의존성 제거하여 중복 호출 방지

  return (
    <div className={cn("relative", className)} style={style}>
      <div id={`map-${id}`} ref={containerRef} className="w-full h-full" />
      {mapState.map && children}
    </div>
  );
};
