import { create } from "zustand";
import { devtools } from "zustand/middleware";

// 이벤트 상태 인터페이스
export interface EventState {
  mousePosition: boolean;
  coordinate: { x: number; y: number } | null;
}

// 이벤트 액션 인터페이스
export interface EventActions {
  setMousePosition: (mousePosition: boolean) => void;
  setCoordinate: (coord: { x: number; y: number }) => void;
}

export const useEventStore = create<EventState & EventActions>()(
  devtools(
    (set) => ({
      mousePosition: false,
      coordinate: null,
      setMousePosition: (mousePosition) =>
        set({ mousePosition }, false, "setMousePosition"),
      setCoordinate: (coordinate) =>
        set({ coordinate }, false, "setCoordinate"),
    }),
    { name: "event-store" },
  ),
);
